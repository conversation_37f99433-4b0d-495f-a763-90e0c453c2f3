<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - SMM Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <!-- Background Animation -->
    <div class="auth-bg">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- Header -->
    <header class="auth-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-6">
                    <a href="index.html" class="auth-logo">
                        <img src="https://via.placeholder.com/150x50/E6CB7C/000000?text=SMM+PANEL" alt="SMM Panel">
                    </a>
                </div>
                <div class="col-6 text-end">
                    <a href="index.html" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="container">
            <div class="row justify-content-center align-items-center min-vh-100">
                <div class="col-lg-10">
                    <div class="auth-container">
                        <div class="row g-0">
                            <!-- Left Side - Info -->
                            <div class="col-lg-6">
                                <div class="auth-info-container">
                                    <div class="auth-info-content">
                                        <div class="info-icon">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <h2>انضم إلى عائلتنا</h2>
                                        <p>أنشئ حسابك الآن واحصل على أفضل خدمات التسويق الرقمي بأسعار لا تقاوم</p>
                                        
                                        <div class="info-features">
                                            <div class="feature-item">
                                                <i class="fas fa-gift"></i>
                                                <span>بونص ترحيبي للأعضاء الجدد</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-crown"></i>
                                                <span>خدمات حصرية ومميزة</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-chart-line"></i>
                                                <span>تقارير مفصلة لطلباتك</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-headset"></i>
                                                <span>دعم فني على مدار الساعة</span>
                                            </div>
                                        </div>

                                        <div class="info-stats">
                                            <div class="stat-item">
                                                <div class="stat-number">+50K</div>
                                                <div class="stat-label">عضو نشط</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number">500+</div>
                                                <div class="stat-label">خدمة متاحة</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number">99%</div>
                                                <div class="stat-label">رضا العملاء</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="auth-image">
                                        <img src="https://via.placeholder.com/400x300/E6CB7C/000000?text=Signup+Image" alt="Signup" class="img-fluid">
                                    </div>
                                </div>
                            </div>

                            <!-- Right Side - Form -->
                            <div class="col-lg-6">
                                <div class="auth-form-container">
                                    <div class="auth-form-header">
                                        <h1 class="auth-title">إنشاء حساب جديد</h1>
                                        <p class="auth-subtitle">املأ البيانات التالية لإنشاء حسابك</p>
                                    </div>

                                    <form class="auth-form" id="signupForm">
                                        <div class="form-group">
                                            <label for="username" class="form-label">
                                                <i class="fas fa-user me-2"></i>
                                                اسم المستخدم
                                            </label>
                                            <input type="text" class="form-control" id="username" name="username" placeholder="أدخل اسم المستخدم" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope me-2"></i>
                                                البريد الإلكتروني
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock me-2"></i>
                                                كلمة المرور
                                            </label>
                                            <div class="password-input">
                                                <input type="password" class="form-control" id="password" name="password" placeholder="أدخل كلمة مرور قوية" required>
                                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="password-strength">
                                                <div class="strength-bar">
                                                    <div class="strength-fill"></div>
                                                </div>
                                                <small class="strength-text">قوة كلمة المرور</small>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="confirmPassword" class="form-label">
                                                <i class="fas fa-lock me-2"></i>
                                                تأكيد كلمة المرور
                                            </label>
                                            <div class="password-input">
                                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="أعد كتابة كلمة المرور" required>
                                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="terms" required>
                                            <label class="form-check-label" for="terms">
                                                أوافق على <a href="#" class="text-warning">الشروط والأحكام</a> و <a href="#" class="text-warning">سياسة الخصوصية</a>
                                            </label>
                                        </div>

                                        <div class="form-check mb-4">
                                            <input class="form-check-input" type="checkbox" id="newsletter">
                                            <label class="form-check-label" for="newsletter">
                                                أريد الحصول على العروض والتحديثات عبر البريد الإلكتروني
                                            </label>
                                        </div>

                                        <button type="submit" class="btn btn-primary btn-auth">
                                            <span class="btn-text">إنشاء الحساب</span>
                                            <span class="btn-loader d-none">
                                                <i class="fas fa-spinner fa-spin"></i>
                                            </span>
                                        </button>

                                        <div class="auth-divider">
                                            <span>أو</span>
                                        </div>

                                        <div class="social-login">
                                            <button type="button" class="btn btn-social btn-google">
                                                <i class="fab fa-google me-2"></i>
                                                التسجيل بـ Google
                                            </button>
                                            <button type="button" class="btn btn-social btn-facebook">
                                                <i class="fab fa-facebook-f me-2"></i>
                                                التسجيل بـ Facebook
                                            </button>
                                        </div>

                                        <div class="auth-footer">
                                            <p>لديك حساب بالفعل؟ <a href="login.html" class="signup-link">تسجيل الدخول</a></p>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/auth.js"></script>

    <script>
        // Toggle Password Visibility
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleBtn = passwordInput.nextElementSibling.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.classList.remove('fa-eye');
                toggleBtn.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleBtn.classList.remove('fa-eye-slash');
                toggleBtn.classList.add('fa-eye');
            }
        }

        // Password Strength Checker
        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = {
                length: password.length >= 8,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                numbers: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            strength = Object.values(checks).filter(Boolean).length;

            return {
                score: strength,
                checks: checks
            };
        }

        // Update Password Strength Indicator
        function updatePasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');

            if (!password) {
                strengthBar.style.width = '0%';
                strengthText.textContent = 'قوة كلمة المرور';
                strengthBar.className = 'strength-fill';
                return;
            }

            const result = checkPasswordStrength(password);
            const percentage = (result.score / 5) * 100;

            strengthBar.style.width = percentage + '%';

            if (result.score <= 2) {
                strengthBar.className = 'strength-fill weak';
                strengthText.textContent = 'ضعيفة';
            } else if (result.score <= 3) {
                strengthBar.className = 'strength-fill medium';
                strengthText.textContent = 'متوسطة';
            } else if (result.score <= 4) {
                strengthBar.className = 'strength-fill good';
                strengthText.textContent = 'جيدة';
            } else {
                strengthBar.className = 'strength-fill strong';
                strengthText.textContent = 'قوية جداً';
            }
        }

        // Form Validation
        function validateForm() {
            const form = document.getElementById('signupForm');
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const terms = document.getElementById('terms').checked;

            // Check if passwords match
            if (password !== confirmPassword) {
                showNotification('كلمات المرور غير متطابقة', 'error');
                return false;
            }

            // Check password strength
            const strength = checkPasswordStrength(password);
            if (strength.score < 3) {
                showNotification('كلمة المرور ضعيفة جداً. يرجى اختيار كلمة مرور أقوى', 'error');
                return false;
            }

            // Check terms acceptance
            if (!terms) {
                showNotification('يجب الموافقة على الشروط والأحكام', 'error');
                return false;
            }

            return true;
        }

        // Form Submission
        const signupForm = document.getElementById('signupForm');
        if (signupForm) {
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                if (password !== confirmPassword) {
                    console.error('Passwords do not match');
                    return;
                }
                fetch('/api/signup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, email, password, confirmPassword })
                })
                .then(res => res.json())
                .then(data => {
                    console.log('Signup response:', data);
                })
                .catch(err => {
                    console.error('Signup error:', err);
                });
            });
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Password strength checker
            document.getElementById('password').addEventListener('input', updatePasswordStrength);

            // Real-time validation
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmPassword = this.value;

                if (confirmPassword && password !== confirmPassword) {
                    this.style.borderColor = '#f44336';
                } else {
                    this.style.borderColor = '';
                }
            });
        });

        // Add CSS for password strength
        const style = document.createElement('style');
        style.textContent = `
            .password-strength {
                margin-top: 8px;
            }

            .strength-bar {
                height: 4px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 5px;
            }

            .strength-fill {
                height: 100%;
                transition: all 0.3s ease;
                border-radius: 2px;
            }

            .strength-fill.weak {
                background: #f44336;
            }

            .strength-fill.medium {
                background: #ff9800;
            }

            .strength-fill.good {
                background: #2196f3;
            }

            .strength-fill.strong {
                background: #4caf50;
            }

            .strength-text {
                color: rgba(255, 255, 255, 0.7);
                font-size: 0.8rem;
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
