/* ===== CSS Variables ===== */
:root {
    --primary-color: #E6CB7C;
    --secondary-color: #F2D98D;
    --accent-color: #F7E4A6;
    --dark-color: #1A1A1A;
    --darker-color: #0F0F0F;
    --light-gray: #F5F5F5;
    --medium-gray: #666666;
    --text-color: #333333;
    --white: #FFFFFF;
    --gradient-primary: linear-gradient(135deg, #E6CB7C 0%, #F2D98D 100%);
    --gradient-secondary: linear-gradient(135deg, #F2D98D 0%, #F7E4A6 100%);
    --gradient-dark: linear-gradient(135deg, #1A1A1A 0%, #0F0F0F 100%);
    --shadow: 0 10px 30px rgba(230, 203, 124, 0.2);
    --shadow-hover: 0 15px 40px rgba(230, 203, 124, 0.3);
}

/* ===== Global Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--dark-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
}

/* ===== Header Styles ===== */
.main-header {
    background: rgba(44, 44, 44, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1050;
}

.navbar-brand .logo {
    height: 40px;
    transition: transform 0.3s ease;
}

.navbar-brand:hover .logo {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 500;
    margin: 0 10px;
    padding: 10px 15px !important;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background: var(--gradient-primary);
    color: var(--dark-color) !important;
    transform: translateY(-2px);
}

.header-buttons .btn {
    margin: 0 5px;
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--dark-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--dark-color);
    transform: translateY(-2px);
}

/* ===== Hero Section ===== */
.hero-section {
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23FFD700" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 100px 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--white);
    margin-bottom: 30px;
    line-height: 1.2;
}

.hero-title .highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-stats {
    margin: 40px 0;
}

.stat-item {
    display: flex;
    align-items: center;
    color: var(--white);
    font-weight: 600;
    margin-bottom: 15px;
}

.stat-item i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-left: 15px;
}

.hero-buttons {
    margin-top: 50px;
}

.hero-buttons .btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    border-radius: 30px;
    margin: 10px 10px 10px 0;
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    background: transparent;
}

.btn-outline-light:hover {
    background: var(--white);
    color: var(--dark-color);
    border-color: var(--white);
}

/* ===== Hero Image ===== */
.hero-image {
    position: relative;
    text-align: center;
}

.main-image {
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.main-image:hover {
    transform: scale(1.05);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-color);
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
    box-shadow: var(--shadow);
}

.floating-1 {
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.floating-2 {
    top: 30%;
    left: 5%;
    animation-delay: 1s;
}

.floating-3 {
    bottom: 30%;
    right: 5%;
    animation-delay: 2s;
}

.floating-4 {
    bottom: 10%;
    left: 15%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* ===== Platforms Section ===== */
.platforms-section {
    padding: 60px 0;
    background: rgba(255, 255, 255, 0.05);
}

.platform-logo {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    margin: 0 15px;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.platform-logo:hover {
    transform: scale(1.1);
    opacity: 1;
}

/* ===== Section Headers ===== */
.section-header {
    margin-bottom: 80px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 20px;
}

.section-line {
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    margin: 0 auto 20px;
    border-radius: 2px;
}

.section-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Features Section ===== */
.features-section {
    padding: 100px 0;
    background: var(--dark-color);
}

.feature-box {
    background: rgba(255, 255, 255, 0.05);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.1);
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.feature-box:hover .feature-icon {
    transform: scale(1.1);
}

.feature-box h4 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-box p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

/* ===== Services Section ===== */
.services-section {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.02);
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.1);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2.5rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-card h4 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.service-card ul {
    list-style: none;
    margin-bottom: 30px;
}

.service-card ul li {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    position: relative;
    padding-right: 20px;
}

.service-card ul li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* ===== Stats Section ===== */
.stats-section {
    padding: 80px 0;
    background: var(--gradient-primary);
}

.stat-box {
    text-align: center;
    color: var(--dark-color);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.8;
}

/* ===== Footer ===== */
.main-footer {
    background: var(--gradient-dark);
    padding: 60px 0 20px;
    border-top: 1px solid rgba(255, 215, 0, 0.2);
}

.footer-widget h5 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-widget p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

.footer-logo {
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.contact-info p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
}

.contact-info i {
    color: var(--primary-color);
    margin-left: 10px;
    width: 20px;
}

.social-links {
    margin-top: 20px;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: var(--primary-color);
    margin-left: 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--dark-color);
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 40px;
    padding-top: 20px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-bottom a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-bottom a:hover {
    color: var(--primary-color);
}

/* ===== Mobile Navigation Styles ===== */
.navbar-toggler {
    border: none;
    padding: 4px 8px;
    background: transparent;
    position: relative;
    z-index: 1001;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: none;
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    position: relative;
    transition: all 0.3s ease;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
    content: '';
    position: absolute;
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.navbar-toggler-icon::before {
    top: -8px;
}

.navbar-toggler-icon::after {
    bottom: -8px;
}

/* Mobile menu styles */
@media (max-width: 991.98px) {
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(26, 26, 26, 0.98);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(230, 203, 124, 0.2);
        padding: 20px;
        margin: 0;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        z-index: 1000;
        display: none;
    }

    .navbar-collapse.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        display: block;
    }

    .navbar-collapse:not(.show) {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    .navbar-nav {
        margin-bottom: 20px;
    }

    .navbar-nav .nav-link {
        padding: 12px 20px !important;
        margin: 5px 0;
        border-radius: 10px;
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(230, 203, 124, 0.1);
    }

    .header-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .header-buttons .btn {
        width: 100%;
        margin: 0;
        padding: 12px 20px;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }

    .floating-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .platform-logo {
        width: 50px;
        height: 50px;
        margin: 0 10px;
    }

    .hero-content {
        padding: 50px 0;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .feature-box,
    .service-card {
        margin-bottom: 30px;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== Smooth Scrolling ===== */
html {
    scroll-behavior: smooth;
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-color);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
