/* ===== Dashboard Styles ===== */
.dashboard-page {
    background: var(--dark-color);
    min-height: 100vh;
    display: flex;
}

/* ===== Sidebar ===== */
.sidebar {
    width: 280px;
    background: var(--gradient-dark);
    border-left: 1px solid rgba(230, 203, 124, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    transform: translateX(100%);
}

.main-content {
    margin-right: 280px;
    transition: margin-right 0.3s ease;
}

.main-content.sidebar-collapsed {
    margin-right: 0;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    height: 40px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(230, 203, 124, 0.1);
}

/* ===== Sidebar Navigation ===== */
.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: rgba(230, 203, 124, 0.1);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--gradient-primary);
    color: var(--dark-color);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--dark-color);
}

.nav-link i {
    width: 20px;
    margin-left: 15px;
    font-size: 1.1rem;
}

.nav-link span {
    font-weight: 500;
}

/* ===== Sidebar Footer ===== */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-balance {
    background: rgba(230, 203, 124, 0.1);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.balance-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.balance-amount {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
}

/* ===== Main Content ===== */
.main-content {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== Dashboard Header ===== */
.dashboard-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(230, 203, 124, 0.1);
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle {
    background: rgba(230, 203, 124, 0.1);
    border: 1px solid rgba(230, 203, 124, 0.2);
    border-radius: 8px;
    color: var(--white);
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
}

.sidebar-toggle:hover {
    background: var(--primary-color);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.sidebar-toggle i {
    font-size: 1.1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.page-title {
    color: var(--white);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* ===== Notifications ===== */
.notification-btn {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    position: relative;
    padding: 10px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.notification-btn:hover {
    background: rgba(230, 203, 124, 0.1);
}

.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #f44336;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* ===== User Header ===== */
.header-user {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.user-info {
    text-align: left;
}

.user-name {
    display: block;
    color: var(--white);
    font-weight: 600;
    font-size: 0.9rem;
}

.user-email {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.dropdown-toggle {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: 5px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--dark-color);
    border: 1px solid rgba(230, 203, 124, 0.2);
    border-radius: 10px;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.header-user:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--white);
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-item:hover {
    background: rgba(230, 203, 124, 0.1);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 20px;
    margin-left: 10px;
}

.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 5px 0;
}

/* ===== Dashboard Content ===== */
.dashboard-content {
    flex: 1;
    padding: 30px;
}

/* ===== Stat Cards ===== */
.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(230, 203, 124, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(230, 203, 124, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--dark-color);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 5px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* ===== Welcome Card Enhanced ===== */
.welcome-card {
    background: var(--gradient-primary);
    border-radius: 25px;
    padding: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--dark-color);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(230, 203, 124, 0.3);
    transition: all 0.3s ease;
}

.welcome-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(230, 203, 124, 0.4);
}

.welcome-bg-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="%23000000" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
    border-radius: 50%;
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.welcome-avatar {
    position: relative;
}

.welcome-avatar img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 3px solid rgba(26, 26, 26, 0.2);
    transition: transform 0.3s ease;
}

.welcome-avatar:hover img {
    transform: scale(1.1);
}

.online-indicator {
    position: absolute;
    bottom: 5px;
    left: 5px;
    width: 18px;
    height: 18px;
    background: #4CAF50;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

.welcome-text h2 {
    font-size: 1.9rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--dark-color);
}

.welcome-text p {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 10px;
}

.welcome-time {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.7;
}

.welcome-stats {
    display: flex;
    gap: 25px;
}

.welcome-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(26, 26, 26, 0.1);
    padding: 20px;
    border-radius: 15px;
    transition: all 0.3s ease;
    min-width: 180px;
}

.welcome-stats .stat-item:hover {
    background: rgba(26, 26, 26, 0.15);
    transform: translateY(-3px);
}

.welcome-stats .stat-icon {
    width: 50px;
    height: 50px;
    background: rgba(26, 26, 26, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: var(--dark-color);
}

.welcome-stats .stat-info {
    flex: 1;
}

.welcome-stats .stat-number {
    font-size: 1.6rem;
    font-weight: 800;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.welcome-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 3px;
}

.welcome-stats .stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.welcome-stats .stat-change.positive {
    color: #4CAF50;
}

.welcome-stats .stat-change.negative {
    color: #f44336;
}

.welcome-stats .stat-change.neutral {
    color: rgba(26, 26, 26, 0.6);
}

/* ===== Enhanced Card Headers ===== */
.card-header-enhanced {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(230, 203, 124, 0.1);
}

.card-header-enhanced .card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--white);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-header-enhanced .card-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.card-header-enhanced .card-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    margin: 0;
}

.performance-period {
    display: flex;
    gap: 8px;
}

.period-btn {
    background: rgba(230, 203, 124, 0.1);
    border: 1px solid rgba(230, 203, 124, 0.2);
    color: rgba(255, 255, 255, 0.7);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.period-btn.active,
.period-btn:hover {
    background: var(--primary-color);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.activity-badge {
    background: var(--primary-color);
    color: var(--dark-color);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* ===== Cards ===== */
.quick-actions-card,
.activity-card,
.performance-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 20px;
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
}

.animate-card {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

.animate-card:nth-child(1) { animation-delay: 0.1s; }
.animate-card:nth-child(2) { animation-delay: 0.2s; }
.animate-card:nth-child(3) { animation-delay: 0.3s; }
.animate-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
    color: var(--white);
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.view-all-link:hover {
    color: var(--secondary-color);
}

/* ===== Quick Actions ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.quick-action-btn {
    background: rgba(230, 203, 124, 0.1);
    border: 1px solid rgba(230, 203, 124, 0.2);
    border-radius: 18px;
    padding: 30px 25px;
    color: var(--white);
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.btn-bg-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 18px;
}

.quick-action-btn.primary .btn-bg-effect {
    opacity: 1;
}

.quick-action-btn:hover .btn-bg-effect {
    opacity: 1;
}

.quick-action-btn.primary {
    color: var(--dark-color);
    border-color: var(--primary-color);
    animation: pulse-primary 2s infinite;
}

@keyframes pulse-primary {
    0% { box-shadow: 0 0 0 0 rgba(230, 203, 124, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(230, 203, 124, 0); }
    100% { box-shadow: 0 0 0 0 rgba(230, 203, 124, 0); }
}

.quick-action-btn:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(230, 203, 124, 0.3);
    border-color: var(--primary-color);
}

.quick-action-btn.primary:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 20px 45px rgba(230, 203, 124, 0.4);
}

.quick-action-btn i {
    font-size: 2.2rem;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.quick-action-btn:hover i {
    transform: scale(1.1);
}

.quick-action-btn span {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.quick-action-btn small {
    font-size: 0.85rem;
    opacity: 0.8;
    line-height: 1.4;
    position: relative;
    z-index: 2;
    max-width: 200px;
}

.btn-arrow {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background: rgba(26, 26, 26, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
    z-index: 2;
}

.quick-action-btn:hover .btn-arrow {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn.primary .btn-arrow {
    background: rgba(26, 26, 26, 0.3);
}

/* ===== Tables ===== */
.orders-table {
    overflow-x: auto;
}

.table {
    color: var(--white);
    margin: 0;
}

.table th {
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    font-size: 0.9rem;
    padding: 15px 10px;
    background: rgba(255, 255, 255, 0.05);
}

.table td {
    border: none;
    padding: 15px 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== Status Badges ===== */
.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-completed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.status-processing {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
    border: 1px solid #FF9800;
}

.status-pending {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
    border: 1px solid #2196F3;
}

.status-cancelled {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid #f44336;
}

/* ===== Notifications List ===== */
.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    color: var(--white);
    font-weight: 500;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* ===== Services Grid ===== */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.service-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    background: rgba(230, 203, 124, 0.1);
    border-color: var(--primary-color);
}

.service-item .service-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.3rem;
    color: var(--dark-color);
}

.service-item h4 {
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.service-item p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.service-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 15px;
}

.service-order-btn {
    background: var(--gradient-primary);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    color: var(--dark-color);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.service-order-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

/* ===== Performance Dashboard ===== */
.performance-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.performance-chart {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(230, 203, 124, 0.1);
}

.performance-metrics {
    display: flex;
    gap: 20px;
    justify-content: space-around;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(230, 203, 124, 0.1);
    flex: 1;
    transition: all 0.3s ease;
}

.metric-item:hover {
    background: rgba(230, 203, 124, 0.05);
    transform: translateY(-3px);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.metric-icon.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.metric-icon.warning {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.metric-icon.info {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
}

.metric-value {
    color: var(--white);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 3px;
}

.metric-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

/* ===== Enhanced Activity List ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 5px;
}

.activity-list::-webkit-scrollbar {
    width: 4px;
}

.activity-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.activity-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 18px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    border: 1px solid rgba(230, 203, 124, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-item:hover::before {
    opacity: 1;
}

.activity-item:hover {
    background: rgba(230, 203, 124, 0.05);
    border-color: rgba(230, 203, 124, 0.2);
    transform: translateX(-5px);
}

.activity-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
    position: relative;
}

.activity-icon.completed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.activity-icon.processing {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: var(--white);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
    line-height: 1.3;
}

.activity-details {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.activity-amount {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.activity-order {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 8px;
    border-radius: 10px;
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 5px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
}

.activity-progress {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 35px;
}

.activity-status {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.activity-status.completed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.activity-status.processing {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.activity-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(230, 203, 124, 0.1);
}

.view-all-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    color: var(--secondary-color);
    transform: translateX(-3px);
}

/* ===== Mini Stats Cards ===== */
.mini-stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 18px;
    padding: 25px 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mini-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mini-stat-card:hover::before {
    opacity: 0.05;
}

.mini-stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 25px rgba(230, 203, 124, 0.2);
}

.mini-stat-icon {
    width: 55px;
    height: 55px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: var(--dark-color);
    flex-shrink: 0;
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.mini-stat-card:hover .mini-stat-icon {
    transform: scale(1.1);
}

.mini-stat-content {
    flex: 1;
    position: relative;
    z-index: 2;
}

.mini-stat-number {
    color: var(--white);
    font-weight: 800;
    font-size: 1.8rem;
    margin-bottom: 5px;
    line-height: 1;
}

.mini-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 8px;
    line-height: 1.2;
}

.mini-stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.mini-stat-trend.up {
    color: #4CAF50;
}

.mini-stat-trend.down {
    color: #f44336;
}

.mini-stat-trend i {
    font-size: 0.7rem;
}

/* ===== Mobile Responsive Fixes ===== */
@media (max-width: 991px) {
    .sidebar {
        transform: translateX(100%);
        width: 260px;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }

    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .dashboard-content {
        padding: 20px;
    }

    .dashboard-header {
        padding: 15px 20px;
        margin-right: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .header-user .user-info {
        display: none;
    }

    .welcome-card {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 25px 20px;
    }

    .welcome-stats {
        justify-content: center;
        gap: 20px;
    }

    .welcome-stats .stat-item {
        min-width: 150px;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .performance-metrics {
        flex-direction: column;
        gap: 15px;
    }

    .metric-item {
        flex-direction: row;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .welcome-card {
        padding: 20px 15px;
    }

    .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .welcome-avatar img {
        width: 60px;
        height: 60px;
    }

    .welcome-text h2 {
        font-size: 1.6rem;
    }

    .welcome-stats {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .welcome-stats .stat-item {
        min-width: auto;
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 15px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .quick-action-btn {
        padding: 20px 15px;
    }

    .quick-action-btn span {
        font-size: 1rem;
    }

    .quick-action-btn small {
        font-size: 0.8rem;
    }

    .performance-card,
    .activity-card {
        padding: 20px 15px;
    }

    .performance-metrics {
        flex-direction: column;
        gap: 12px;
    }

    .metric-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
        padding: 12px;
    }

    .mini-stat-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        padding: 15px;
    }

    .mini-stat-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }

    .mini-stat-number {
        font-size: 1.5rem;
    }

    .activity-item {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 15px;
    }

    .activity-content {
        order: 1;
    }

    .activity-icon {
        order: 2;
        align-self: center;
        margin-bottom: 0;
    }

    .activity-status {
        order: 3;
        align-self: center;
    }

    .activity-progress {
        justify-content: center;
        margin-top: 8px;
    }

    .dashboard-header {
        padding: 12px 15px;
    }

    .header-user .user-info {
        display: none;
    }

    .page-title {
        font-size: 1.4rem;
    }

    .card-header-enhanced {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }

    .card-header-enhanced .card-title {
        font-size: 1.2rem;
    }

    .performance-period {
        flex-wrap: wrap;
        gap: 6px;
    }

    .period-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .welcome-card {
        padding: 15px;
        margin-bottom: 20px;
    }

    .welcome-content {
        gap: 12px;
    }

    .welcome-text h2 {
        font-size: 1.4rem;
        margin-bottom: 6px;
    }

    .welcome-text p {
        font-size: 1rem;
    }

    .welcome-stats {
        gap: 12px;
    }

    .welcome-stats .stat-item {
        padding: 12px;
        gap: 8px;
    }

    .welcome-stats .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .welcome-stats .stat-number {
        font-size: 1.4rem;
    }

    .quick-actions {
        gap: 12px;
    }

    .quick-action-btn {
        padding: 18px 12px;
        gap: 12px;
    }

    .quick-action-btn i {
        font-size: 1.8rem;
    }

    .quick-action-btn span {
        font-size: 0.95rem;
    }

    .quick-action-btn small {
        font-size: 0.75rem;
        max-width: 180px;
    }

    .performance-card,
    .activity-card {
        padding: 15px;
    }

    .card-header-enhanced {
        margin-bottom: 15px;
        padding-bottom: 12px;
    }

    .card-header-enhanced .card-title {
        font-size: 1.1rem;
        gap: 8px;
    }

    .card-header-enhanced .card-subtitle {
        font-size: 0.85rem;
    }

    .performance-chart {
        padding: 15px;
    }

    .performance-metrics {
        gap: 10px;
    }

    .metric-item {
        padding: 10px;
        gap: 6px;
    }

    .metric-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .metric-value {
        font-size: 1rem;
    }

    .metric-label {
        font-size: 0.8rem;
    }

    .mini-stat-card {
        padding: 12px;
        gap: 10px;
    }

    .mini-stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .mini-stat-number {
        font-size: 1.3rem;
    }

    .mini-stat-label {
        font-size: 0.85rem;
    }

    .mini-stat-trend {
        font-size: 0.75rem;
    }

    .activity-list {
        gap: 12px;
        max-height: 300px;
    }

    .activity-item {
        padding: 12px;
        gap: 10px;
    }

    .activity-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .activity-title {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .activity-amount {
        font-size: 0.85rem;
    }

    .activity-order {
        font-size: 0.8rem;
        padding: 1px 6px;
    }

    .activity-time {
        font-size: 0.75rem;
    }

    .activity-status {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }

    .progress-bar {
        height: 4px;
    }

    .progress-text {
        font-size: 0.75rem;
        min-width: 30px;
    }

    .activity-footer {
        margin-top: 15px;
        padding-top: 12px;
    }

    .view-all-btn {
        font-size: 0.85rem;
        gap: 6px;
    }

    .dashboard-header {
        padding: 10px 15px;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .header-actions {
        gap: 8px;
    }

    .header-actions .btn {
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    .period-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .activity-badge {
        padding: 3px 8px;
        font-size: 0.75rem;
    }
}

/* ===== Loading States & Skeleton Screens ===== */
.skeleton {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
}

.skeleton-text.large {
    height: 24px;
}

.skeleton-text.small {
    height: 12px;
}

.skeleton-circle {
    border-radius: 50%;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(230, 203, 124, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Pulse Animation for Primary Button ===== */
.pulse-animation {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 0 0 rgba(230, 203, 124, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(230, 203, 124, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(230, 203, 124, 0);
    }
}

/* ===== Smooth Transitions ===== */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* ===== Focus States ===== */
.quick-action-btn:focus,
.period-btn:focus,
button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== Accessibility ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
