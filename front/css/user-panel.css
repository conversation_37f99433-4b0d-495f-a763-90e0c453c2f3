/* ===== User Panel Styles ===== */
.user-panel-page {
    background: var(--gradient-dark);
    min-height: 100vh;
    color: var(--white);
}

/* ===== Header ===== */
.user-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(230, 203, 124, 0.1);
    padding: 20px 0;
}

.logo img {
    height: 45px;
}

/* ===== User Balance Card ===== */
.user-balance-card {
    background: var(--gradient-primary);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--dark-color);
    box-shadow: 0 10px 30px rgba(230, 203, 124, 0.3);
}

.balance-info {
    display: flex;
    flex-direction: column;
}

.balance-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.balance-amount {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--dark-color);
}

.btn-add-funds {
    background: rgba(26, 26, 26, 0.2);
    border: none;
    color: var(--dark-color);
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-add-funds:hover {
    background: rgba(26, 26, 26, 0.3);
    transform: translateY(-2px);
}

/* ===== User Menu ===== */
.user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    color: var(--white);
    font-weight: 600;
    font-size: 0.95rem;
}

.user-email {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* ===== Main Content ===== */
.user-main {
    padding: 40px 0;
}

/* ===== Welcome Section ===== */
.welcome-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 20px;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.welcome-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 10px;
}

.welcome-content p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    margin: 0;
}

.quick-stats {
    display: flex;
    gap: 30px;
}

.quick-stats .stat-item {
    text-align: center;
}

.quick-stats .stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.quick-stats .stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* ===== Services Header ===== */
.services-header {
    text-align: center;
    margin-bottom: 30px;
}

.services-header h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 10px;
}

.services-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
}

/* ===== Platform Tabs ===== */
.platform-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.platform-tab {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 15px;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.platform-tab:hover {
    background: rgba(230, 203, 124, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.platform-tab.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: var(--dark-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(230, 203, 124, 0.3);
}

.platform-tab i {
    font-size: 1.2rem;
}

/* ===== Services Grid ===== */
.services-container {
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

/* ===== Service Card ===== */
.service-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 20px;
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 40px rgba(230, 203, 124, 0.2);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--dark-color);
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.service-info {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.service-info h3 {
    color: var(--white);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.service-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    margin-bottom: 15px;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

.feature i {
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* ===== Service Pricing ===== */
.service-pricing {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.price {
    color: var(--primary-color);
    font-size: 1.4rem;
    font-weight: 800;
    margin-bottom: 8px;
}

.min-order,
.max-order {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-bottom: 3px;
}

/* ===== Order Button ===== */
.btn-order {
    width: 100%;
    background: var(--gradient-primary);
    border: none;
    color: var(--dark-color);
    padding: 15px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.btn-order:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(230, 203, 124, 0.4);
    color: var(--dark-color);
}

/* ===== Recent Orders ===== */
.recent-orders-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 20px;
    padding: 30px;
}

.recent-orders-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(230, 203, 124, 0.1);
}

.recent-orders-card h3 {
    color: var(--white);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.view-all-link:hover {
    color: var(--secondary-color);
}

/* ===== Orders List ===== */
.orders-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.order-item:hover {
    background: rgba(230, 203, 124, 0.05);
    border-color: rgba(230, 203, 124, 0.2);
    transform: translateX(-5px);
}

.order-service {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.order-service .service-icon {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--dark-color);
    margin-bottom: 0;
}

.service-details {
    display: flex;
    flex-direction: column;
}

.service-name {
    color: var(--white);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 3px;
}

.service-link {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

.order-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.order-quantity {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
}

.order-status {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.order-status.completed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.order-status.processing {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
    border: 1px solid #FF9800;
}

.order-status.pending {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
    border: 1px solid #2196F3;
}

.order-meta {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: 5px;
}

.order-date {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

.order-price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1rem;
}

/* ===== Modal Styles ===== */
.modal-content {
    background: var(--dark-color);
    border: 1px solid rgba(230, 203, 124, 0.2);
    border-radius: 20px;
    color: var(--white);
}

.modal-header {
    border-bottom: 1px solid rgba(230, 203, 124, 0.1);
    padding: 25px 30px 20px;
}

.modal-title {
    color: var(--white);
    font-weight: 700;
    font-size: 1.4rem;
}

.btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-body {
    padding: 25px 30px;
}

.modal-footer {
    border-top: 1px solid rgba(230, 203, 124, 0.1);
    padding: 20px 30px 25px;
}

/* ===== Service Summary in Modal ===== */
.service-summary {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(230, 203, 124, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
}

.service-icon-large {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--dark-color);
}

.service-summary .service-details h4 {
    color: var(--white);
    font-weight: 700;
    margin-bottom: 8px;
}

.service-summary .service-details p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
}

.service-price-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.service-price-info .price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.service-price-info .limits {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* ===== Form Styles ===== */
.form-label {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(230, 203, 124, 0.2);
    border-radius: 10px;
    color: var(--white);
    padding: 12px 15px;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 203, 124, 0.25);
    color: var(--white);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* ===== Order Calculation ===== */
.order-calculation {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    color: rgba(255, 255, 255, 0.8);
}

.calculation-row.total {
    border-top: 1px solid rgba(230, 203, 124, 0.2);
    margin-top: 10px;
    padding-top: 15px;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.balance-check {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding: 10px 15px;
    background: rgba(230, 203, 124, 0.1);
    border-radius: 8px;
    color: var(--primary-color);
    font-weight: 600;
}

/* ===== Order Notes ===== */
.order-notes {
    margin-top: 20px;
}

.order-notes textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== Payment Methods ===== */
.payment-methods h6,
.amount-selection h6 {
    color: var(--white);
    font-weight: 700;
    margin-bottom: 15px;
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 25px;
}

.payment-option {
    position: relative;
}

.payment-option input[type="radio"] {
    display: none;
}

.payment-option label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.payment-option input[type="radio"]:checked + label {
    background: rgba(230, 203, 124, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.payment-option label i {
    font-size: 1.3rem;
}

/* ===== Amount Selection ===== */
.amount-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.amount-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(230, 203, 124, 0.1);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amount-btn:hover,
.amount-btn.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: var(--dark-color);
    transform: translateY(-2px);
}

.custom-amount {
    margin-top: 15px;
}

/* ===== Payment Summary ===== */
.payment-summary {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    color: rgba(255, 255, 255, 0.8);
}

.summary-row.total {
    border-top: 1px solid rgba(230, 203, 124, 0.2);
    margin-top: 10px;
    padding-top: 15px;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* ===== Responsive Design ===== */
@media (max-width: 991px) {
    .user-balance-card {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .welcome-section {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .quick-stats {
        justify-content: center;
        gap: 20px;
    }

    .platform-tabs {
        gap: 10px;
    }

    .platform-tab {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .order-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .order-service {
        justify-content: center;
    }

    .order-meta {
        align-items: center;
    }
}

@media (max-width: 576px) {
    .user-header {
        padding: 15px 0;
    }

    .user-main {
        padding: 20px 0;
    }

    .welcome-section {
        padding: 20px;
    }

    .welcome-content h1 {
        font-size: 1.6rem;
    }

    .services-header h2 {
        font-size: 1.8rem;
    }

    .platform-tabs {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
    }

    .quick-stats {
        flex-direction: column;
        gap: 15px;
    }

    .user-menu .user-details {
        display: none;
    }

    .amount-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-body,
    .modal-header,
    .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
}
