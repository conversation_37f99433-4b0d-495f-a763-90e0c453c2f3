/* ===== Auth Page Styles ===== */
.auth-page {
    background: var(--gradient-dark);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* ===== Background Animation ===== */
.auth-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.15;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 1s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 50%;
    animation-delay: 3s;
}

/* ===== Auth Header ===== */
.auth-header {
    padding: 20px 0;
    position: relative;
    z-index: 10;
}

.auth-logo img {
    height: 40px;
    transition: transform 0.3s ease;
}

.auth-logo:hover img {
    transform: scale(1.05);
}

/* ===== Auth Container ===== */
.auth-main {
    position: relative;
    z-index: 5;
    padding: 40px 0;
}

.auth-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    min-height: 600px;
}

/* ===== Form Container ===== */
.auth-form-container {
    padding: 60px 40px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-form-header {
    text-align: center;
    margin-bottom: 40px;
}

.auth-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 10px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.test-data-notice {
    background: rgba(230, 203, 124, 0.1);
    border: 1px solid rgba(230, 203, 124, 0.3);
    border-radius: 8px;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-bottom: 0;
}

.test-data-notice i {
    font-size: 1rem;
}

/* ===== Form Styles ===== */
.auth-form {
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.form-label i {
    color: var(--primary-color);
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 10px;
    padding: 15px 20px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    color: var(--white);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}

.form-control[value]:not([value=""]) {
    background: rgba(230, 203, 124, 0.05);
    border-color: rgba(230, 203, 124, 0.3);
}

/* ===== Password Input ===== */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* ===== Form Options ===== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.form-check-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.form-check-input:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.forgot-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: var(--secondary-color);
}

/* ===== Auth Button ===== */
.btn-auth {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    background: var(--gradient-primary);
    border: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.btn-auth:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* ===== Divider ===== */
.auth-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
}

.auth-divider span {
    background: var(--dark-color);
    color: rgba(255, 255, 255, 0.6);
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

/* ===== Social Login ===== */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.btn-social {
    padding: 12px 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    color: var(--white);
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-google:hover {
    background: #db4437;
    border-color: #db4437;
}

.btn-facebook:hover {
    background: #3b5998;
    border-color: #3b5998;
}

/* ===== Auth Footer ===== */
.auth-footer {
    text-align: center;
}

.auth-footer p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.signup-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.signup-link:hover {
    color: var(--secondary-color);
}

/* ===== Info Container ===== */
.auth-info-container {
    background: var(--gradient-primary);
    padding: 60px 40px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.auth-info-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23000000" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.auth-info-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--dark-color);
}

.info-icon {
    width: 80px;
    height: 80px;
    background: rgba(44, 44, 44, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 2rem;
    color: var(--dark-color);
}

.auth-info-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.auth-info-content p {
    font-size: 1.1rem;
    margin-bottom: 40px;
    opacity: 0.8;
    line-height: 1.6;
}

/* ===== Info Features ===== */
.info-features {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-weight: 500;
}

.feature-item i {
    margin-left: 10px;
    color: var(--dark-color);
}

/* ===== Info Stats ===== */
.info-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ===== Auth Image ===== */
.auth-image {
    text-align: center;
    position: relative;
    z-index: 2;
}

.auth-image img {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.auth-image:hover img {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    border-radius: 0 0 15px 15px;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.auth-image:hover .image-overlay {
    transform: translateY(0);
}

.overlay-content h4 {
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.overlay-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
}

/* ===== Responsive Design ===== */
@media (max-width: 991px) {
    .auth-container {
        margin: 20px;
    }
    
    .auth-form-container,
    .auth-info-container {
        padding: 40px 30px;
    }
    
    .auth-title {
        font-size: 2rem;
    }
    
    .info-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .auth-form-container,
    .auth-info-container {
        padding: 30px 20px;
    }
    
    .auth-title {
        font-size: 1.8rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .social-login {
        gap: 10px;
    }
    
    .info-stats {
        flex-direction: column;
        gap: 15px;
    }
}

/* ===== Animation Keyframes ===== */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Form Animation ===== */
.auth-form-container,
.auth-info-container {
    animation: fadeInUp 0.8s ease-out;
}

.auth-form-container {
    animation-delay: 0.2s;
}

.auth-info-container {
    animation-delay: 0.4s;
}
