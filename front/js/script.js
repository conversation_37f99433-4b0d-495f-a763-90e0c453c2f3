// ===== Performance Optimization =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== DOM Content Loaded =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functions
    initNavbar();
    initScrollAnimations();
    initCounters();
    initSmoothScroll();
    initParallax();
    initTypingEffect();
    initMobileMenu();
    initLoadingAnimation();
    initFormValidation();
    initScrollToTop();
    initButtonHoverEffects();
});

// ===== Navbar Functions =====
function initNavbar() {
    const navbar = document.querySelector('.main-header');
    const navLinks = document.querySelectorAll('.nav-link');

    if (!navbar) return;

    // Navbar scroll effect
    const handleScroll = debounce(function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(44, 44, 44, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(44, 44, 44, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    }, 10);

    // Active nav link on scroll
    const handleActiveLink = debounce(function() {
        let current = '';
        const sections = document.querySelectorAll('section[id]');

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }, 10);

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('scroll', handleActiveLink);
}

// ===== Smooth Scroll =====
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== Scroll Animations =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';

                // Add stagger effect for multiple elements
                if (entry.target.classList.contains('feature-box') ||
                    entry.target.classList.contains('service-card')) {
                    const siblings = Array.from(entry.target.parentNode.children);
                    const index = siblings.indexOf(entry.target);
                    const delay = index * 100;
                    entry.target.style.transitionDelay = delay + 'ms';
                }

                // Unobserve after animation
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements
    const animatedElements = document.querySelectorAll('.feature-box, .service-card, .stat-box, .section-header');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease-out';
        observer.observe(el);
    });
}

// ===== Counter Animation =====
function initCounters() {
    const counters = document.querySelectorAll('.stat-number');
    const speed = 200;

    const observerOptions = {
        threshold: 0.5
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const originalText = counter.textContent;
                const target = parseInt(originalText.replace(/[^\d]/g, ''));

                if (target && !counter.classList.contains('counted')) {
                    counter.classList.add('counted');
                    const increment = target / speed;
                    let current = 0;

                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            counter.textContent = formatNumber(target) + getUnit(originalText);
                            clearInterval(timer);
                        } else {
                            counter.textContent = formatNumber(Math.floor(current)) + getUnit(originalText);
                        }
                    }, 10);
                }

                observer.unobserve(counter);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// ===== Helper Functions =====
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(0) + 'K';
    }
    return num.toString();
}

function getUnit(text) {
    if (text.includes('K')) return 'K+';
    if (text.includes('M')) return 'M+';
    if (text.includes('/')) return '/7';
    return '+';
}

// ===== Parallax Effect =====
function initParallax() {
    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-icon');

        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// ===== Typing Effect =====
function initTypingEffect() {
    const heroTitle = document.querySelector('.hero-title');
    if (!heroTitle) return;

    // Get the text content only (without HTML tags)
    const fullText = "أقوى منصة في الوطن العربي لخدمات التسويق الرقمي";

    // Set the initial HTML with the highlight span and empty text
    heroTitle.innerHTML = '<span class="highlight">اسم المنصة</span> <span class="typing-text"></span>';

    const typingElement = heroTitle.querySelector('.typing-text');
    if (!typingElement) return;

    let i = 0;
    const typeWriter = () => {
        if (i < fullText.length && typingElement) {
            typingElement.textContent += fullText.charAt(i);
            i++;
            setTimeout(typeWriter, 50);
        }
    };

    // Start typing effect after a delay
    setTimeout(typeWriter, 500);
}

// ===== Button Hover Effects =====
function initButtonHoverEffects() {
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// ===== Mobile Menu Toggle =====
function initMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        let isMenuOpen = false;

        // Create custom toggle functionality
        navbarToggler.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isMenuOpen) {
                closeMenu();
            } else {
                openMenu();
            }
        });

        function openMenu() {
            navbarCollapse.classList.add('show');
            navbarCollapse.style.display = 'block';
            isMenuOpen = true;

            // Add animation class
            setTimeout(() => {
                navbarCollapse.style.opacity = '1';
                navbarCollapse.style.visibility = 'visible';
                navbarCollapse.style.transform = 'translateY(0)';
            }, 10);
        }

        function closeMenu() {
            navbarCollapse.style.opacity = '0';
            navbarCollapse.style.visibility = 'hidden';
            navbarCollapse.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                navbarCollapse.classList.remove('show');
                navbarCollapse.style.display = 'none';
                isMenuOpen = false;
            }, 300);
        }

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMenu();
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (isMenuOpen && !navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                closeMenu();
            }
        });

        // Close menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 991.98 && isMenuOpen) {
                closeMenu();
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isMenuOpen) {
                closeMenu();
            }
        });
    }
}

// ===== Loading Animation =====
function initLoadingAnimation() {
    window.addEventListener('load', function() {
        const loader = document.querySelector('.loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => {
                loader.style.display = 'none';
            }, 500);
        }
        
        // Animate hero content
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.style.opacity = '1';
            heroContent.style.transform = 'translateY(0)';
        }
    });
}

// ===== Form Validation =====
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const inputs = form.querySelectorAll('input[required], textarea[required]');
            let isValid = true;
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('error');
                    showError(input, 'هذا الحقل مطلوب');
                } else {
                    input.classList.remove('error');
                    hideError(input);
                }
                if (input.type === 'email' && input.value.trim()) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(input.value)) {
                        isValid = false;
                        input.classList.add('error');
                        showError(input, 'يرجى إدخال بريد إلكتروني صحيح');
                    }
                }
            });
            if (isValid) {
                // لا تظهر أي رسالة نجاح أو إشعار
                form.reset();
            }
        });
    });
}

// ===== Error/Success Messages =====
function showError(input, message) {
    let errorElement = input.nextElementSibling;
    if (!errorElement || !errorElement.classList.contains('error-message')) {
        errorElement = document.createElement('div');
        errorElement.classList.add('error-message');
        input.parentNode.insertBefore(errorElement, input.nextSibling);
    }
    errorElement.textContent = message;
    errorElement.style.color = '#ff4444';
    errorElement.style.fontSize = '0.8rem';
    errorElement.style.marginTop = '5px';
}

function hideError(input) {
    const errorElement = input.nextElementSibling;
    if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.remove();
    }
}

// ===== Scroll to Top Button =====
function initScrollToTop() {
    const scrollBtn = document.createElement('button');
    scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollBtn.classList.add('scroll-to-top');
    scrollBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 55px;
        height: 55px;
        background: linear-gradient(135deg, #E6CB7C 0%, #F2D98D 100%);
        border: 2px solid rgba(230, 203, 124, 0.3);
        border-radius: 50%;
        color: #1A1A1A;
        font-size: 1.3rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(230, 203, 124, 0.3);
    `;

    document.body.appendChild(scrollBtn);

    // Hover effects
    scrollBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.1)';
        this.style.boxShadow = '0 8px 25px rgba(230, 203, 124, 0.4)';
    });

    scrollBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 15px rgba(230, 203, 124, 0.3)';
    });

    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollBtn.style.opacity = '1';
            scrollBtn.style.visibility = 'visible';
        } else {
            scrollBtn.style.opacity = '0';
            scrollBtn.style.visibility = 'hidden';
        }
    });

    scrollBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// ===== Initialize Button Hover Effects =====
// This is now called in the main DOMContentLoaded event

// ===== End of Script =====
// All functions have been initialized in the main DOMContentLoaded event
