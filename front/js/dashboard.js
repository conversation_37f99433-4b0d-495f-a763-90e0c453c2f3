// ===== Dashboard JavaScript =====

// ===== Dashboard Class =====
class Dashboard {
    constructor() {
        this.init();
    }
    
    init() {
        this.initSidebar();
        this.initQuickActions();
        this.initNotifications();
        this.initUserDropdown();
        this.initServiceOrders();
        this.initPeriodButtons();
        this.loadDashboardData();
    }

    // ===== Period Buttons =====
    initPeriodButtons() {
        const periodBtns = document.querySelectorAll('.period-btn');

        periodBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // Remove active class from all buttons
                periodBtns.forEach(b => b.classList.remove('active'));

                // Add active class to clicked button
                btn.classList.add('active');

                // Update chart based on period
                const period = btn.getAttribute('data-period');
                this.updatePerformanceChart(period);
            });
        });
    }

    // ===== Update Performance Chart =====
    updatePerformanceChart(period) {
        const canvas = document.getElementById('performanceChart');
        if (!canvas) return;

        // Show loading animation
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw loading spinner
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        ctx.beginPath();
        ctx.arc(centerX, centerY, 20, 0, Math.PI * 1.5);
        ctx.strokeStyle = '#E6CB7C';
        ctx.lineWidth = 3;
        ctx.stroke();

        // Simulate data loading
        setTimeout(() => {
            let data;
            switch(period) {
                case 'week':
                    data = [20, 45, 30, 60, 40, 80, 55];
                    break;
                case 'month':
                    data = [30, 55, 40, 70, 50, 90, 65, 100, 80, 95];
                    break;
                case 'year':
                    data = [40, 65, 50, 80, 60, 100, 75, 110, 90, 105, 85, 120];
                    break;
                default:
                    data = [20, 45, 30, 60, 40, 80, 55];
            }

            this.drawChart(canvas, data);
        }, 500);
    }
    
    // ===== Sidebar Management =====
    initSidebar() {
        const sidebarToggles = document.querySelectorAll('.sidebar-toggle');
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        let mobileOverlay = document.querySelector('.mobile-overlay');

        // Create mobile overlay if it doesn't exist
        if (!mobileOverlay) {
            mobileOverlay = document.createElement('div');
            mobileOverlay.className = 'mobile-overlay';
            document.body.appendChild(mobileOverlay);
        }

        sidebarToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                if (window.innerWidth <= 991) {
                    // Mobile behavior
                    sidebar.classList.toggle('active');
                    mobileOverlay.classList.toggle('active');

                    // Prevent body scroll when sidebar is open
                    if (sidebar.classList.contains('active')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                } else {
                    // Desktop behavior - hide/show sidebar
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('sidebar-collapsed');
                }
            });
        });

        // Close sidebar when clicking overlay (mobile only)
        mobileOverlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            mobileOverlay.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 991) {
                sidebar.classList.remove('active');
                mobileOverlay.classList.remove('active');
                document.body.style.overflow = '';
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('sidebar-collapsed');
            }
        });
        
        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                link.classList.add('active');
                // Get target section
                const target = link.getAttribute('href').substring(1);
                // Update page title
                const pageTitle = document.querySelector('.page-title');
                const linkText = link.querySelector('span').textContent;
                if (pageTitle) {
                    pageTitle.textContent = linkText;
                }
                // Load content for the section
                this.loadContent(target);
                // Close sidebar on mobile
                if (window.innerWidth <= 991) {
                    sidebar.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    }

    // ===== Load Content =====
    loadContent(section) {
        // Hide all content sections
        const contentSections = document.querySelectorAll('.dashboard-content > .row');
        contentSections.forEach(section => {
            section.style.display = 'none';
        });

        // Show specific content based on section
        switch(section) {
            case 'dashboard':
                // Show all dashboard content
                contentSections.forEach(section => {
                    section.style.display = 'block';
                });
                break;
            case 'new-order':
                this.showNewOrderModal();
                break;
            case 'orders':
                this.showOrdersContent();
                break;
            case 'services':
                this.showServicesContent();
                break;
            case 'add-funds':
                this.showAddFundsModal();
                break;
            case 'support':
                this.showSupportContent();
                break;
            default:
                // Show dashboard by default
                contentSections.forEach(section => {
                    section.style.display = 'block';
                });
        }
    }

    // ===== Show Orders Content =====
    showOrdersContent() {
        const dashboardContent = document.querySelector('.dashboard-content');
        dashboardContent.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="orders-page-card">
                        <div class="card-header">
                            <h3>جميع طلباتك</h3>
                            <div class="filter-buttons">
                                <button class="btn btn-outline-primary active" data-filter="all">الكل</button>
                                <button class="btn btn-outline-primary" data-filter="completed">مكتملة</button>
                                <button class="btn btn-outline-primary" data-filter="processing">قيد التنفيذ</button>
                                <button class="btn btn-outline-primary" data-filter="pending">في الانتظار</button>
                            </div>
                        </div>
                        <div class="orders-table">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>الخدمة</th>
                                            <th>الرابط</th>
                                            <th>الكمية</th>
                                            <th>التكلفة</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>#12345</td>
                                            <td>متابعين إنستغرام</td>
                                            <td>@username</td>
                                            <td>1,000</td>
                                            <td>$0.50</td>
                                            <td><span class="status completed">مكتمل</span></td>
                                            <td>2024-01-15</td>
                                        </tr>
                                        <tr>
                                            <td>#12344</td>
                                            <td>مشاهدات يوتيوب</td>
                                            <td>youtube.com/watch?v=...</td>
                                            <td>5,000</td>
                                            <td>$1.50</td>
                                            <td><span class="status processing">قيد التنفيذ</span></td>
                                            <td>2024-01-14</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ===== Show Services Content =====
    showServicesContent() {
        const dashboardContent = document.querySelector('.dashboard-content');
        dashboardContent.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="services-page-card">
                        <div class="card-header">
                            <h3>جميع الخدمات المتاحة</h3>
                            <p>اختر الخدمة التي تريدها من القائمة أدناه</p>
                        </div>
                        <div class="services-grid">
                            <div class="service-category">
                                <h4><i class="fab fa-instagram"></i> Instagram</h4>
                                <div class="services-list">
                                    <div class="service-item">
                                        <span class="service-name">متابعين إنستغرام</span>
                                        <span class="service-price">$0.50 / 1000</span>
                                        <button class="btn btn-primary btn-sm">اطلب الآن</button>
                                    </div>
                                    <div class="service-item">
                                        <span class="service-name">لايكات إنستغرام</span>
                                        <span class="service-price">$0.30 / 1000</span>
                                        <button class="btn btn-primary btn-sm">اطلب الآن</button>
                                    </div>
                                </div>
                            </div>
                            <div class="service-category">
                                <h4><i class="fab fa-youtube"></i> YouTube</h4>
                                <div class="services-list">
                                    <div class="service-item">
                                        <span class="service-name">مشاهدات يوتيوب</span>
                                        <span class="service-price">$0.25 / 1000</span>
                                        <button class="btn btn-primary btn-sm">اطلب الآن</button>
                                    </div>
                                    <div class="service-item">
                                        <span class="service-name">مشتركين يوتيوب</span>
                                        <span class="service-price">$1.20 / 1000</span>
                                        <button class="btn btn-primary btn-sm">اطلب الآن</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ===== Show Support Content =====
    showSupportContent() {
        const dashboardContent = document.querySelector('.dashboard-content');
        dashboardContent.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="support-page-card">
                        <div class="card-header">
                            <h3>الدعم الفني</h3>
                            <p>نحن هنا لمساعدتك في أي وقت</p>
                        </div>
                        <div class="support-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="contact-methods">
                                        <h5>طرق التواصل</h5>
                                        <div class="contact-item">
                                            <i class="fab fa-whatsapp"></i>
                                            <span>واتساب: +1234567890</span>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <span>البريد: <EMAIL></span>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fab fa-telegram"></i>
                                            <span>تليجرام: @smmSupport</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="support-form">
                                        <h5>أرسل رسالة</h5>
                                        <form>
                                            <div class="mb-3">
                                                <label class="form-label">الموضوع</label>
                                                <input type="text" class="form-control" placeholder="موضوع الرسالة">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">الرسالة</label>
                                                <textarea class="form-control" rows="4" placeholder="اكتب رسالتك هنا..."></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ===== Quick Actions =====
    initQuickActions() {
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (event) => {
                const action = btn.getAttribute('data-action');
                this.handleQuickAction(action, event);
            });
        });
    }

    handleQuickAction(action, event) {
        const button = event.target.closest('.quick-action-btn');
        // Add click animation
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
        // Show loading state
        const originalContent = button.innerHTML;
        button.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <span>جاري التحميل...</span>
        `;
        button.disabled = true;
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
            switch(action) {
                case 'new-order':
                    this.showNewOrderModal();
                    break;
                case 'add-funds':
                    this.showAddFundsModal();
                    break;
                case 'orders':
                    this.loadContent('orders');
                    break;
                case 'services':
                    this.loadContent('services');
                    break;
            }
        }, 1000);
    }

    // ===== Notifications =====
    initNotifications() {
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.showNotificationsPanel();
            });
        }
    }

    showNotificationsPanel() {
        // You can remove this method or leave it empty if you want to remove notifications
    }

    // ===== User Dropdown =====
    initUserDropdown() {
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const href = item.getAttribute('href');
                if (href === '#logout') {
                    this.handleLogout();
                } else {
                    this.loadContent(href.substring(1));
                }
            });
        });
    }

    handleLogout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
        }
    }

    // ===== Service Orders =====
    initServiceOrders() {
        const serviceOrderBtns = document.querySelectorAll('.service-order-btn');
        serviceOrderBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const serviceItem = btn.closest('.service-item');
                const serviceName = serviceItem.querySelector('h4').textContent;
                this.showOrderModal(serviceName);
            });
        });
    }

    showOrderModal(serviceName) {
        const modal = document.createElement('div');
        modal.className = 'order-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>طلب خدمة: ${serviceName}</h3>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="order-form">
                        <div class="form-group">
                            <label>رابط الحساب/المنشور</label>
                            <input type="url" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>الكمية</label>
                            <input type="number" class="form-control" min="100" max="10000" required>
                        </div>
                        <div class="form-group">
                            <label>التكلفة المتوقعة</label>
                            <div class="cost-display">$0.00</div>
                        </div>
                        <button type="submit" class="btn btn-primary">تأكيد الطلب</button>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        // Close modal
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });
        modal.querySelector('.modal-overlay').addEventListener('click', () => {
            modal.remove();
        });
        // Handle form submission
        modal.querySelector('.order-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitOrder(modal);
        });
    }

    submitOrder(modal) {
        setTimeout(() => {
            modal.remove();
            this.updateStats();
        }, 2000);
    }

    // ===== Data Loading =====
    loadDashboardData() {
        // Simulate loading dashboard data
        this.updateCurrentTime();
        this.animateCounters();
        this.animateWelcomeStats();
        this.animateMiniStats();
        this.loadRecentOrders();
        this.loadNotifications();
        this.initPerformanceChart();
    }

    // ===== Current Time Update =====
    updateCurrentTime() {
        const timeElement = document.getElementById('current-time');
        if (!timeElement) return;
        const updateTime = () => {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            timeElement.textContent = now.toLocaleDateString('ar-SA', options);
        };
        updateTime();
        setInterval(updateTime, 60000); // Update every minute
    }

    // ===== Welcome Stats Animation =====
    animateWelcomeStats() {
        const statNumbers = document.querySelectorAll('.welcome-stats .stat-number');
        statNumbers.forEach((stat, index) => {
            const target = parseFloat(stat.getAttribute('data-target'));
            const isDecimal = target % 1 !== 0;
            setTimeout(() => {
                this.animateNumber(stat, 0, target, 2000, isDecimal);
            }, index * 200);
        });
    }

    // ===== Mini Stats Animation =====
    animateMiniStats() {
        const miniStats = document.querySelectorAll('.mini-stat-number');
        miniStats.forEach((stat, index) => {
            const target = parseInt(stat.getAttribute('data-target'));
            setTimeout(() => {
                this.animateNumber(stat, 0, target, 1500, false);
            }, index * 150);
        });
    }

    // ===== Number Animation Helper =====
    animateNumber(element, start, end, duration, isDecimal = false) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            // Easing function for smooth animation
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const current = start + (end - start) * easeOutCubic;
            if (isDecimal) {
                element.textContent = '$' + current.toFixed(2);
            } else {
                element.textContent = Math.floor(current);
            }
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        requestAnimationFrame(animate);
    }

    // ===== Performance Chart =====
    initPerformanceChart() {
        const canvas = document.getElementById('performanceChart');
        if (!canvas) return;
        // Initial data for week view
        const data = [20, 45, 30, 60, 40, 80, 55];
        this.drawChart(canvas, data);
    }

    // ===== Draw Chart Function =====
    drawChart(canvas, data) {
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        const maxValue = Math.max(...data);
        const padding = 20;
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        // Draw gradient background
        const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
        gradient.addColorStop(0, 'rgba(230, 203, 124, 0.3)');
        gradient.addColorStop(1, 'rgba(230, 203, 124, 0.05)');
        // Draw grid lines
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        // Horizontal grid lines
        for (let i = 0; i <= 4; i++) {
            const y = padding + (i * (height - 2 * padding)) / 4;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(width - padding, y);
            ctx.stroke();
        }
        // Vertical grid lines
        for (let i = 0; i < data.length; i++) {
            const x = padding + (i * (width - 2 * padding)) / (data.length - 1);
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, height - padding);
            ctx.stroke();
        }
        // Draw line chart with animation
        ctx.beginPath();
        ctx.strokeStyle = '#E6CB7C';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        const points = [];
        data.forEach((value, index) => {
            const x = padding + (index * (width - 2 * padding)) / (data.length - 1);
            const y = height - padding - ((value / maxValue) * (height - 2 * padding));
            points.push({ x, y });
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();
        // Fill area under curve
        ctx.lineTo(points[points.length - 1].x, height - padding);
        ctx.lineTo(points[0].x, height - padding);
        ctx.closePath();
        ctx.fillStyle = gradient;
        ctx.fill();
        // Draw data points with hover effect
        points.forEach((point, index) => {
            ctx.beginPath();
            ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI);
            ctx.fillStyle = '#E6CB7C';
            ctx.fill();
            ctx.strokeStyle = '#1A1A1A';
            ctx.lineWidth = 2;
            ctx.stroke();
            // Add glow effect
            ctx.beginPath();
            ctx.arc(point.x, point.y, 8, 0, 2 * Math.PI);
            ctx.strokeStyle = 'rgba(230, 203, 124, 0.5)';
            ctx.lineWidth = 1;
            ctx.stroke();
        });
        // Add value labels
        ctx.fillStyle = '#E6CB7C';
        ctx.font = '12px Cairo, sans-serif';
        ctx.textAlign = 'center';
        points.forEach((point, index) => {
            ctx.fillText(data[index], point.x, point.y - 15);
        });
    }

    animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
            const increment = target / 50;
            let current = 0;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = this.formatNumber(target);
                    clearInterval(timer);
                } else {
                    counter.textContent = this.formatNumber(Math.floor(current));
                }
            }, 20);
        });
    }

    formatNumber(num) {
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    loadRecentOrders() {
        // Simulate loading recent orders
        const tbody = document.querySelector('.orders-table tbody');
        if (tbody) {
            // Add loading animation
            tbody.style.opacity = '0.5';
            setTimeout(() => {
                tbody.style.opacity = '1';
            }, 1000);
        }
    }

    loadNotifications() {
        // Simulate loading notifications
        const notificationsList = document.querySelector('.notifications-list');
        if (notificationsList) {
            notificationsList.style.opacity = '0.5';
            setTimeout(() => {
                notificationsList.style.opacity = '1';
            }, 800);
        }
    }

    updateStats() {
        // Update statistics after new order
        const totalOrders = document.querySelector('.stat-card .stat-number');
        if (totalOrders) {
            const current = parseInt(totalOrders.textContent);
            totalOrders.textContent = current + 1;
        }
    }

    // ===== Modals =====
    showNewOrderModal() {
        // Placeholder for new order modal
    }

    showAddFundsModal() {
        // Placeholder for add funds modal
    }

    showSupportModal() {
        // Placeholder for support modal
    }
}

// ===== Initialize Dashboard =====
document.addEventListener('DOMContentLoaded', function() {
    new Dashboard();
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideInDown {
            from {
                transform: translate(-50%, -100%);
                opacity: 0;
            }
            to {
                transform: translate(-50%, 0);
                opacity: 1;
            }
        }
        @keyframes slideOutUp {
            from {
                transform: translate(-50%, 0);
                opacity: 1;
            }
            to {
                transform: translate(-50%, -100%);
                opacity: 0;
            }
        }
        .order-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1002;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background: var(--dark-color);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            position: relative;
            z-index: 1;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .modal-header h3 {
            color: var(--white);
            margin: 0;
        }
        .modal-close {
            background: none;
            border: none;
            color: var(--white);
            font-size: 1.2rem;
            cursor: pointer;
        }
        .modal-body {
            padding: 20px;
        }
        .order-form .form-group {
            margin-bottom: 20px;
        }
        .order-form label {
            color: var(--white);
            margin-bottom: 8px;
            display: block;
        }
        .order-form .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: var(--white);
            width: 100%;
        }
        .cost-display {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: var(--primary-color);
            font-weight: 600;
        }
        .notifications-panel {
            background: var(--dark-color);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 10px;
        }
        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .panel-header h3 {
            color: var(--white);
            margin: 0;
            font-size: 1.1rem;
        }
        .panel-close {
            background: none;
            border: none;
            color: var(--white);
            cursor: pointer;
        }
        .panel-content {
            padding: 15px 20px;
            max-height: 400px;
            overflow-y: auto;
        }
    `;
    document.head.appendChild(style);
});
