// ===== Authentication JavaScript =====

// ===== Form Validation =====
class FormValidator {
    constructor(form) {
        this.form = form;
        this.errors = {};
        this.init();
    }
    
    init() {
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.addRealTimeValidation();
    }
    
    handleSubmit(e) {
        e.preventDefault();
        this.errors = {};
        if (this.validateForm()) {
            // No login simulation, no toast, no redirect
        }
    }
    
    validateForm() {
        const inputs = this.form.querySelectorAll('input[required]');
        let isValid = true;
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        return isValid;
    }
    
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const name = field.name;
        // Required field validation
        if (!value) {
            this.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
        // Email validation
        if (type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }
        }
        // Phone validation
        if (type === 'tel') {
            const phoneRegex = /^[\+]?\d{10,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
                return false;
            }
        }
        // Password validation
        if (name === 'password') {
            if (value.length < 8) {
                this.showFieldError(field, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return false;
            }
        }
        // Confirm password validation
        if (name === 'confirmPassword') {
            const password = this.form.querySelector('input[name="password"]').value;
            if (value !== password) {
                this.showFieldError(field, 'كلمات المرور غير متطابقة');
                return false;
            }
        }
        this.hideFieldError(field);
        return true;
    }
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: #f44336;
            font-size: 0.8rem;
            margin-top: 5px;
            display: block;
        `;
    }
    hideFieldError(field) {
        field.classList.remove('is-invalid');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }
    addRealTimeValidation() {
        const inputs = this.form.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }
}

// ===== Password Strength Checker =====
class PasswordStrength {
    constructor(passwordField) {
        this.passwordField = passwordField;
        this.init();
    }
    init() {
        this.createStrengthIndicator();
        this.passwordField.addEventListener('input', () => this.updateStrength());
    }
    createStrengthIndicator() {
        const strengthContainer = document.createElement('div');
        strengthContainer.className = 'password-strength';
        strengthContainer.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <small class="strength-text">قوة كلمة المرور</small>
        `;
        this.passwordField.parentNode.appendChild(strengthContainer);
        this.strengthBar = strengthContainer.querySelector('.strength-fill');
        this.strengthText = strengthContainer.querySelector('.strength-text');
    }
    checkStrength(password) {
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        score = Object.values(checks).filter(Boolean).length;
        return { score, checks };
    }
    updateStrength() {
        const password = this.passwordField.value;
        if (!password) {
            this.strengthBar.style.width = '0%';
            this.strengthText.textContent = 'قوة كلمة المرور';
            this.strengthBar.className = 'strength-fill';
            return;
        }
        const result = this.checkStrength(password);
        const percentage = (result.score / 5) * 100;
        this.strengthBar.style.width = percentage + '%';
        if (result.score <= 2) {
            this.strengthBar.className = 'strength-fill weak';
            this.strengthText.textContent = 'ضعيفة';
        } else if (result.score <= 3) {
            this.strengthBar.className = 'strength-fill medium';
            this.strengthText.textContent = 'متوسطة';
        } else if (result.score <= 4) {
            this.strengthBar.className = 'strength-fill good';
            this.strengthText.textContent = 'جيدة';
        } else {
            this.strengthBar.className = 'strength-fill strong';
            this.strengthText.textContent = 'قوية جداً';
        }
    }
}

// ===== Password Toggle =====
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleBtn = passwordInput.nextElementSibling.querySelector('i');
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.classList.remove('fa-eye');
        toggleBtn.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleBtn.classList.remove('fa-eye-slash');
        toggleBtn.classList.add('fa-eye');
    }
}

// ===== Social Login =====
function handleSocialLogin(provider) {
    // No toast, no redirect
}

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');
    if (loginForm) {
        new FormValidator(loginForm);
    }
    if (signupForm) {
        new FormValidator(signupForm);
        const passwordField = signupForm.querySelector('input[name="password"]');
        if (passwordField) {
            new PasswordStrength(passwordField);
        }
    }
    const socialBtns = document.querySelectorAll('.btn-social');
    socialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const provider = this.textContent.includes('Google') ? 'Google' : 'Facebook';
            handleSocialLogin(provider);
        });
    });
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        .password-strength {
            margin-top: 8px;
        }
    `;
    document.head.appendChild(style);
});
