function showToast(message, type = "success") {
const colors = {
    success: '#28a745',
    error: '#dc3545',
    info: '#007bff'
};
const bgColor = colors[type] || colors.success;

const toast = document.createElement("div");
toast.innerHTML = `
    <div style="
    background: ${bgColor};
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 10px;
    min-width: 200px;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: fadeIn 0.4s ease;
    ">
    <span>${message}</span>
    <button onclick="this.parentElement.remove()" style="
        background: none;
        border: none;
        color: white;
        font-size: 16px;
        cursor: pointer;
    ">&times;</button>
    </div>
`;
document.getElementById("toast-container").appendChild(toast);

setTimeout(() => {
    toast.remove();
}, 4000);
}


