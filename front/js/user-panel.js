// ===== User Panel JavaScript =====

// ===== User Panel Class =====
class UserPanel {
    constructor() {
        this.currentPlatform = 'instagram';
        this.userBalance = 125.50;
        this.services = this.initServices();
        this.init();
    }
    
    init() {
        this.initPlatformTabs();
        this.initServiceCards();
        this.initOrderModal();
        this.initAddFundsModal();
        this.loadPlatformServices('instagram');
    }
    
    // ===== Initialize Services Data =====
    initServices() {
        return {
            instagram: [
                {
                    id: 'instagram-followers',
                    name: 'متابعين إنستغرام',
                    description: 'متابعين حقيقيين عالي الجودة',
                    icon: 'fas fa-users',
                    price: 0.50,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 50000,
                    features: ['جودة عالية', 'تسليم سريع', 'ضمان عدم النقصان']
                },
                {
                    id: 'instagram-likes',
                    name: 'لايكات إنستغرام',
                    description: 'لايكات سريعة وآمنة للمنشورات',
                    icon: 'fas fa-heart',
                    price: 0.30,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 100000,
                    features: ['تسليم فوري', 'آمن 100%', 'جودة عالية']
                },
                {
                    id: 'instagram-views',
                    name: 'مشاهدات إنستغرام',
                    description: 'مشاهدات للريلز والفيديوهات',
                    icon: 'fas fa-eye',
                    price: 0.20,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 1000000,
                    features: ['مشاهدات حقيقية', 'تسليم سريع', 'أسعار منافسة']
                },
                {
                    id: 'instagram-comments',
                    name: 'تعليقات إنستغرام',
                    description: 'تعليقات مخصصة وذات معنى',
                    icon: 'fas fa-comments',
                    price: 2.00,
                    priceUnit: '100',
                    minOrder: 10,
                    maxOrder: 1000,
                    features: ['تعليقات مخصصة', 'باللغة العربية', 'جودة عالية']
                }
            ],
            youtube: [
                {
                    id: 'youtube-views',
                    name: 'مشاهدات يوتيوب',
                    description: 'مشاهدات عالية الجودة للفيديوهات',
                    icon: 'fas fa-play',
                    price: 0.25,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 1000000,
                    features: ['مشاهدات حقيقية', 'تسليم تدريجي', 'آمن 100%']
                },
                {
                    id: 'youtube-subscribers',
                    name: 'مشتركين يوتيوب',
                    description: 'مشتركين نشطين للقناة',
                    icon: 'fas fa-user-plus',
                    price: 1.20,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 10000,
                    features: ['مشتركين حقيقيين', 'نشطين ومتفاعلين', 'ضمان الجودة']
                },
                {
                    id: 'youtube-likes',
                    name: 'لايكات يوتيوب',
                    description: 'لايكات للفيديوهات',
                    icon: 'fas fa-thumbs-up',
                    price: 0.40,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 50000,
                    features: ['تسليم سريع', 'جودة عالية', 'آمن تماماً']
                }
            ],
            tiktok: [
                {
                    id: 'tiktok-followers',
                    name: 'متابعين تيك توك',
                    description: 'متابعين نشطين ومتفاعلين',
                    icon: 'fas fa-users',
                    price: 0.80,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 20000,
                    features: ['متابعين حقيقيين', 'تفاعل عالي', 'تسليم سريع']
                },
                {
                    id: 'tiktok-likes',
                    name: 'لايكات تيك توك',
                    description: 'لايكات سريعة وآمنة',
                    icon: 'fas fa-heart',
                    price: 0.35,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 100000,
                    features: ['تسليم فوري', 'جودة عالية', 'آمن 100%']
                },
                {
                    id: 'tiktok-views',
                    name: 'مشاهدات تيك توك',
                    description: 'مشاهدات عالية الجودة',
                    icon: 'fas fa-eye',
                    price: 0.15,
                    priceUnit: '1000',
                    minOrder: 1000,
                    maxOrder: 10000000,
                    features: ['مشاهدات حقيقية', 'تسليم سريع', 'أسعار ممتازة']
                }
            ],
            twitter: [
                {
                    id: 'twitter-followers',
                    name: 'متابعين تويتر',
                    description: 'متابعين نشطين ومتفاعلين',
                    icon: 'fas fa-users',
                    price: 1.50,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 10000,
                    features: ['متابعين حقيقيين', 'نشطين ومتفاعلين', 'جودة عالية']
                },
                {
                    id: 'twitter-likes',
                    name: 'لايكات تويتر',
                    description: 'لايكات للتغريدات',
                    icon: 'fas fa-heart',
                    price: 0.60,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 50000,
                    features: ['تسليم سريع', 'آمن 100%', 'جودة عالية']
                },
                {
                    id: 'twitter-retweets',
                    name: 'إعادة تغريد',
                    description: 'إعادة تغريد للمنشورات',
                    icon: 'fas fa-retweet',
                    price: 0.80,
                    priceUnit: '1000',
                    minOrder: 25,
                    maxOrder: 25000,
                    features: ['تسليم سريع', 'حسابات حقيقية', 'آمن تماماً']
                }
            ],
            facebook: [
                {
                    id: 'facebook-likes',
                    name: 'لايكات فيسبوك',
                    description: 'لايكات للصفحات والمنشورات',
                    icon: 'fas fa-thumbs-up',
                    price: 0.70,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 50000,
                    features: ['لايكات حقيقية', 'تسليم سريع', 'جودة عالية']
                },
                {
                    id: 'facebook-followers',
                    name: 'متابعين فيسبوك',
                    description: 'متابعين للصفحات',
                    icon: 'fas fa-users',
                    price: 1.00,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 20000,
                    features: ['متابعين حقيقيين', 'نشطين', 'ضمان الجودة']
                }
            ],
            snapchat: [
                {
                    id: 'snapchat-followers',
                    name: 'متابعين سناب شات',
                    description: 'متابعين نشطين للحساب',
                    icon: 'fas fa-users',
                    price: 2.00,
                    priceUnit: '1000',
                    minOrder: 50,
                    maxOrder: 5000,
                    features: ['متابعين حقيقيين', 'نشطين', 'جودة عالية']
                },
                {
                    id: 'snapchat-views',
                    name: 'مشاهدات سناب شات',
                    description: 'مشاهدات للقصص',
                    icon: 'fas fa-eye',
                    price: 0.50,
                    priceUnit: '1000',
                    minOrder: 100,
                    maxOrder: 100000,
                    features: ['مشاهدات حقيقية', 'تسليم سريع', 'آمن 100%']
                }
            ]
        };
    }
    
    // ===== Platform Tabs =====
    initPlatformTabs() {
        const platformTabs = document.querySelectorAll('.platform-tab');
        
        platformTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                platformTabs.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                tab.classList.add('active');
                
                // Get platform
                const platform = tab.getAttribute('data-platform');
                this.currentPlatform = platform;
                
                // Load services for this platform
                this.loadPlatformServices(platform);
            });
        });
    }
    
    // ===== Load Platform Services =====
    loadPlatformServices(platform) {
        // Hide all service grids
        const allGrids = document.querySelectorAll('.services-grid');
        allGrids.forEach(grid => {
            grid.classList.add('d-none');
        });
        
        // Show current platform grid
        const currentGrid = document.getElementById(`${platform}-services`);
        if (currentGrid) {
            currentGrid.classList.remove('d-none');
            
            // If it's not Instagram (which is pre-loaded), generate the services
            if (platform !== 'instagram') {
                this.generateServiceCards(platform, currentGrid);
            }
        }
    }
    
    // ===== Generate Service Cards =====
    generateServiceCards(platform, container) {
        const services = this.services[platform] || [];
        
        container.innerHTML = services.map(service => `
            <div class="service-card" data-service="${service.id}">
                <div class="service-icon">
                    <i class="${service.icon}"></i>
                </div>
                <div class="service-info">
                    <h3>${service.name}</h3>
                    <p>${service.description}</p>
                    <div class="service-features">
                        ${service.features.map(feature => `
                            <span class="feature">
                                <i class="fas fa-check"></i> ${feature}
                            </span>
                        `).join('')}
                    </div>
                </div>
                <div class="service-pricing">
                    <div class="price">$${service.price.toFixed(2)} / ${service.priceUnit}</div>
                    <div class="min-order">الحد الأدنى: ${service.minOrder.toLocaleString()}</div>
                    <div class="max-order">الحد الأقصى: ${service.maxOrder.toLocaleString()}</div>
                </div>
                <button class="btn btn-primary btn-order" data-service="${service.id}">
                    اطلب الآن
                </button>
            </div>
        `).join('');
        
        // Re-initialize service cards
        this.initServiceCards();
    }

    // ===== Initialize Service Cards =====
    initServiceCards() {
        const orderButtons = document.querySelectorAll('.btn-order');

        orderButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const serviceId = button.getAttribute('data-service');
                this.openOrderModal(serviceId);
            });
        });
    }

    // ===== Open Order Modal =====
    openOrderModal(serviceId) {
        const service = this.findServiceById(serviceId);
        if (!service) return;

        // Update modal content
        document.getElementById('modalServiceName').textContent = service.name;
        document.getElementById('modalServiceDesc').textContent = service.description;
        document.getElementById('modalServicePrice').textContent = `$${service.price.toFixed(2)} / ${service.priceUnit}`;
        document.getElementById('modalServiceLimits').textContent = `الحد الأدنى: ${service.minOrder.toLocaleString()} | الحد الأقصى: ${service.maxOrder.toLocaleString()}`;

        // Update icon
        const modalIcon = document.querySelector('.service-icon-large i');
        modalIcon.className = service.icon;

        // Set form values
        document.getElementById('serviceQuantity').min = service.minOrder;
        document.getElementById('serviceQuantity').max = service.maxOrder;
        document.getElementById('serviceQuantity').value = service.minOrder;

        // Calculate initial cost
        this.calculateOrderCost(service);

        // Store service data
        document.getElementById('orderForm').setAttribute('data-service', serviceId);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('orderModal'));
        modal.show();
    }

    // ===== Find Service by ID =====
    findServiceById(serviceId) {
        for (const platform in this.services) {
            const service = this.services[platform].find(s => s.id === serviceId);
            if (service) return service;
        }
        return null;
    }

    // ===== Initialize Order Modal =====
    initOrderModal() {
        const quantityInput = document.getElementById('serviceQuantity');
        const confirmButton = document.getElementById('confirmOrder');

        // Quantity change event
        quantityInput.addEventListener('input', () => {
            const serviceId = document.getElementById('orderForm').getAttribute('data-service');
            const service = this.findServiceById(serviceId);
            if (service) {
                this.calculateOrderCost(service);
            }
        });

        // Confirm order event
        confirmButton.addEventListener('click', () => {
            this.confirmOrder();
        });
    }

    // ===== Calculate Order Cost =====
    calculateOrderCost(service) {
        const quantity = parseInt(document.getElementById('serviceQuantity').value) || 0;
        const pricePerUnit = service.price;
        const unitSize = parseInt(service.priceUnit);

        const totalCost = (quantity / unitSize) * pricePerUnit;

        // Update calculation display
        document.getElementById('calcQuantity').textContent = quantity.toLocaleString();
        document.getElementById('calcPrice').textContent = `$${pricePerUnit.toFixed(2)}`;
        document.getElementById('calcTotal').textContent = `$${totalCost.toFixed(2)}`;
        document.getElementById('currentBalance').textContent = `$${this.userBalance.toFixed(2)}`;

        // Check if user has enough balance
        const confirmButton = document.getElementById('confirmOrder');
        if (totalCost > this.userBalance) {
            confirmButton.disabled = true;
            confirmButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> رصيد غير كافي';
            confirmButton.classList.add('btn-danger');
            confirmButton.classList.remove('btn-primary');
        } else {
            confirmButton.disabled = false;
            confirmButton.innerHTML = '<i class="fas fa-shopping-cart"></i> تأكيد الطلب';
            confirmButton.classList.remove('btn-danger');
            confirmButton.classList.add('btn-primary');
        }
    }

    // ===== Confirm Order =====
    confirmOrder() {
        const serviceId = document.getElementById('orderForm').getAttribute('data-service');
        const service = this.findServiceById(serviceId);
        const quantity = parseInt(document.getElementById('serviceQuantity').value);
        const link = document.getElementById('serviceLink').value;
        const notes = document.getElementById('orderNotes').value;

        if (!service || !quantity || !link) {
            return;
        }

        // Validate URL
        if (!this.isValidUrl(link)) {
            return;
        }

        // Calculate cost
        const unitSize = parseInt(service.priceUnit);
        const totalCost = (quantity / unitSize) * service.price;

        if (totalCost > this.userBalance) {
            return;
        }

        // Show loading
        const confirmButton = document.getElementById('confirmOrder');
        const originalText = confirmButton.innerHTML;
        confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
        confirmButton.disabled = true;

        // Simulate order processing
        setTimeout(() => {
            // Deduct from balance
            this.userBalance -= totalCost;
            this.updateBalanceDisplay();

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
            modal.hide();

            // Reset form
            document.getElementById('orderForm').reset();

            // Add to recent orders
            this.addToRecentOrders(service, quantity, totalCost, link);

            // Reset button
            confirmButton.innerHTML = originalText;
            confirmButton.disabled = false;
        }, 2000);
    }

    // ===== Validate URL =====
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    // ===== Update Balance Display =====
    updateBalanceDisplay() {
        const balanceElements = document.querySelectorAll('#userBalance, #currentBalance');
        balanceElements.forEach(element => {
            element.textContent = `$${this.userBalance.toFixed(2)}`;
        });
    }

    // ===== Add to Recent Orders =====
    addToRecentOrders(service, quantity, cost, link) {
        const ordersList = document.querySelector('.orders-list');
        const newOrder = document.createElement('div');
        newOrder.className = 'order-item';

        // Get platform icon
        const platformIcon = this.getPlatformIcon(service.id);

        newOrder.innerHTML = `
            <div class="order-service">
                <div class="service-icon">
                    <i class="${platformIcon}"></i>
                </div>
                <div class="service-details">
                    <div class="service-name">${service.name}</div>
                    <div class="service-link">${this.truncateUrl(link)}</div>
                </div>
            </div>
            <div class="order-info">
                <div class="order-quantity">${quantity.toLocaleString()}</div>
                <div class="order-status processing">قيد التنفيذ</div>
            </div>
            <div class="order-meta">
                <div class="order-date">${new Date().toLocaleDateString('ar-SA')}</div>
                <div class="order-price">$${cost.toFixed(2)}</div>
            </div>
        `;

        // Add animation
        newOrder.style.opacity = '0';
        newOrder.style.transform = 'translateX(20px)';

        ordersList.insertBefore(newOrder, ordersList.firstChild);

        // Animate in
        setTimeout(() => {
            newOrder.style.transition = 'all 0.3s ease';
            newOrder.style.opacity = '1';
            newOrder.style.transform = 'translateX(0)';
        }, 100);

        // Remove oldest if more than 5
        const orders = ordersList.querySelectorAll('.order-item');
        if (orders.length > 5) {
            const lastOrder = orders[orders.length - 1];
            lastOrder.style.transition = 'all 0.3s ease';
            lastOrder.style.opacity = '0';
            lastOrder.style.transform = 'translateX(-20px)';

            setTimeout(() => {
                lastOrder.remove();
            }, 300);
        }
    }

    // ===== Get Platform Icon =====
    getPlatformIcon(serviceId) {
        if (serviceId.includes('instagram')) return 'fab fa-instagram';
        if (serviceId.includes('youtube')) return 'fab fa-youtube';
        if (serviceId.includes('tiktok')) return 'fab fa-tiktok';
        if (serviceId.includes('twitter')) return 'fab fa-twitter';
        if (serviceId.includes('facebook')) return 'fab fa-facebook';
        if (serviceId.includes('snapchat')) return 'fab fa-snapchat';
        return 'fas fa-globe';
    }

    // ===== Truncate URL =====
    truncateUrl(url) {
        if (url.length > 30) {
            return url.substring(0, 30) + '...';
        }
        return url;
    }

    // ===== Initialize Add Funds Modal =====
    initAddFundsModal() {
        const amountButtons = document.querySelectorAll('.amount-btn');
        const customAmountInput = document.getElementById('customAmount');
        const proceedButton = document.getElementById('proceedPayment');

        // Amount button events
        amountButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                amountButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                button.classList.add('active');

                // Clear custom amount
                customAmountInput.value = '';

                // Update payment summary
                const amount = parseFloat(button.getAttribute('data-amount'));
                this.updatePaymentSummary(amount);
            });
        });

        // Custom amount input event
        customAmountInput.addEventListener('input', () => {
            // Remove active class from all buttons
            amountButtons.forEach(btn => btn.classList.remove('active'));

            // Update payment summary
            const amount = parseFloat(customAmountInput.value) || 0;
            this.updatePaymentSummary(amount);
        });

        // Proceed payment event
        proceedButton.addEventListener('click', () => {
            this.proceedPayment();
        });
    }

    // ===== Update Payment Summary =====
    updatePaymentSummary(amount) {
        const processingFee = amount * 0.05; // 5% processing fee
        const total = amount + processingFee;

        document.getElementById('paymentAmount').textContent = `$${amount.toFixed(2)}`;
        document.getElementById('processingFee').textContent = `$${processingFee.toFixed(2)}`;
        document.getElementById('totalPayment').textContent = `$${total.toFixed(2)}`;

        // Enable/disable proceed button
        const proceedButton = document.getElementById('proceedPayment');
        if (amount >= 5) {
            proceedButton.disabled = false;
            proceedButton.classList.remove('btn-secondary');
            proceedButton.classList.add('btn-primary');
        } else {
            proceedButton.disabled = true;
            proceedButton.classList.add('btn-secondary');
            proceedButton.classList.remove('btn-primary');
        }
    }

    // ===== Proceed Payment =====
    proceedPayment() {
        const selectedAmount = this.getSelectedAmount();
        const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;

        if (selectedAmount < 5) {
            return;
        }

        // Show loading
        const proceedButton = document.getElementById('proceedPayment');
        const originalText = proceedButton.innerHTML;
        proceedButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
        proceedButton.disabled = true;

        // Simulate payment processing
        setTimeout(() => {
            // Add to balance
            this.userBalance += selectedAmount;
            this.updateBalanceDisplay();

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addFundsModal'));
            modal.hide();

            // Reset form
            this.resetAddFundsForm();

            // Reset button
            proceedButton.innerHTML = originalText;
            proceedButton.disabled = false;
        }, 3000);
    }

    // ===== Get Selected Amount =====
    getSelectedAmount() {
        const activeButton = document.querySelector('.amount-btn.active');
        const customAmount = document.getElementById('customAmount').value;

        if (activeButton) {
            return parseFloat(activeButton.getAttribute('data-amount'));
        } else if (customAmount) {
            return parseFloat(customAmount);
        }

        return 0;
    }

    // ===== Reset Add Funds Form =====
    resetAddFundsForm() {
        // Reset amount buttons
        document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector('.amount-btn[data-amount="50"]').classList.add('active');

        // Reset custom amount
        document.getElementById('customAmount').value = '';

        // Reset payment method
        document.getElementById('paypal').checked = true;

        // Reset summary
        this.updatePaymentSummary(50);
    }
}

// ===== Initialize User Panel =====
document.addEventListener('DOMContentLoaded', function() {
    new UserPanel();

    // Add notification styles
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--dark-color);
            border: 1px solid rgba(230, 203, 124, 0.3);
            border-radius: 12px;
            padding: 15px 20px;
            color: var(--white);
            z-index: 9999;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-success {
            border-color: #4CAF50;
        }

        .notification-success .notification-content i {
            color: #4CAF50;
        }

        .notification-error {
            border-color: #f44336;
        }

        .notification-error .notification-content i {
            color: #f44336;
        }

        .notification-warning {
            border-color: #FF9800;
        }

        .notification-warning .notification-content i {
            color: #FF9800;
        }

        .notification-close {
            position: absolute;
            top: 10px;
            left: 10px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 0.9rem;
        }

        .notification-close:hover {
            color: var(--white);
        }
    `;
    document.head.appendChild(style);
});
