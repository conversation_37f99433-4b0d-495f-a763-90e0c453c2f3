<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - SMM Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <!-- Background Animation -->
    <div class="auth-bg">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- Header -->
    <header class="auth-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-6">
                    <a href="index.html" class="auth-logo">
                        <img src="https://via.placeholder.com/150x50/E6CB7C/000000?text=SMM+PANEL" alt="SMM Panel">
                    </a>
                </div>
                <div class="col-6 text-end">
                    <a href="index.html" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="container">
            <div class="row justify-content-center align-items-center min-vh-100">
                <div class="col-lg-10">
                    <div class="auth-container">
                        <div class="row g-0">
                            <!-- Left Side - Form -->
                            <div class="col-lg-6">
                                <div class="auth-form-container">
                                    <div class="auth-form-header">
                                        <h1 class="auth-title">مرحباً بعودتك!</h1>
                                        <p class="auth-subtitle">سجل دخولك للوصول إلى لوحة التحكم</p>
                                    </div>

                                    <form class="auth-form" id="loginForm" method="post">
                                        <div class="form-group">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope me-2"></i>
                                                البريد الإلكتروني
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock me-2"></i>
                                                كلمة المرور
                                            </label>
                                            <div class="password-input">
                                                <input type="password" class="form-control" id="password" name="password" placeholder="123456" required>
                                                <button type="button" class="password-toggle" onclick="togglePassword()">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="form-options">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="remember">
                                                <label class="form-check-label" for="remember">
                                                    تذكرني
                                                </label>
                                            </div>
                                            <a href="forgot-password.html" class="forgot-link">نسيت كلمة المرور؟</a>
                                        </div>

                                        <button type="submit" class="btn btn-primary btn-auth">
                                            <span class="btn-text">تسجيل الدخول</span>
                                            <span class="btn-loader d-none">
                                                <i class="fas fa-spinner fa-spin"></i>
                                            </span>
                                        </button>

                                        <div class="auth-divider">
                                            <span>أو</span>
                                        </div>

                                        <div class="social-login">
                                            <button type="button" class="btn btn-social btn-google">
                                                <i class="fab fa-google me-2"></i>
                                                تسجيل الدخول بـ Google
                                            </button>
                                            <button type="button" class="btn btn-social btn-facebook">
                                                <i class="fab fa-facebook-f me-2"></i>
                                                تسجيل الدخول بـ Facebook
                                            </button>
                                        </div>

                                        <div class="auth-footer">
                                            <p>ليس لديك حساب؟ <a href="signup.html" class="signup-link">إنشاء حساب جديد</a></p>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Right Side - Info -->
                            <div class="col-lg-6">
                                <div class="auth-info-container">
                                    <div class="auth-info-content">
                                        <div class="info-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <h2>نمّي حساباتك بذكاء</h2>
                                        <p>انضم إلى أكثر من 50,000 مسوق ناجح واحصل على نتائج حقيقية ومضمونة لحساباتك على جميع منصات التواصل الاجتماعي</p>
                                        
                                        <div class="info-features">
                                            <div class="feature-item">
                                                <i class="fas fa-shield-check"></i>
                                                <span>حماية كاملة لحساباتك</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-bolt"></i>
                                                <span>تسليم فوري خلال دقائق</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-medal"></i>
                                                <span>جودة عالمية مضمونة</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-headset"></i>
                                                <span>دعم فني 24/7 باللغة العربية</span>
                                            </div>
                                        </div>

                                        <div class="info-stats">
                                            <div class="stat-item">
                                                <div class="stat-number">75K+</div>
                                                <div class="stat-label">عميل راضي</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number">2.5M+</div>
                                                <div class="stat-label">طلب منجز</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number">99.8%</div>
                                                <div class="stat-label">معدل النجاح</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="auth-image">
                                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center" alt="Digital Marketing Dashboard" class="img-fluid">
                                        <div class="image-overlay">
                                            <div class="overlay-content">
                                                <h4>لوحة تحكم احترافية</h4>
                                                <p>إدارة شاملة لجميع حملاتك التسويقية</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        // Toggle Password Visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.classList.remove('fa-eye');
                toggleBtn.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleBtn.classList.remove('fa-eye-slash');
                toggleBtn.classList.add('fa-eye');
            }
        }

        // Show Notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // AJAX Login Submit
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                })
                .then(res => res.json())
                .then(data => {
                    console.log('Login response:', data);
                })
                .catch(err => {
                    console.error('Login error:', err);
                });
            });
        }
    </script>
</body>
</html>
