const User = require('../models/User')

/**
 * @desc Render Services Page
 * @route /service
 * @method GET
 * @access Public
*/
module.exports.renderServices = async (req, res) => {
  try{
    const userId = req.userId;

    // Fetch User With User Id
    const user = await User.findOne({ _id : userId }).lean();
    return res.status(200).render('user/service', {
      // User Data
      userUsername : user.username,
      userEmail : user.email,
      userBalance : user.balance
    })
  }catch (err) {
    console.log(err.message)
    req.flash('error', '🔥 حصلت مشكلة غير متوقعة، حاول تاني!');
    res.status(500).redirect('/service');
  };
};

// name: "🔥 لايكات تيك توك - تعويض مدى الحياة",
// category: "TikTok",
// pricePerUnit: 0.08, // سعر البيع للمستخدم - انت حر فيه
  // minQuantity: 10,
  // maxQuantity: 1000000,
  
  // apiServiceId: 1234 