const User = require('../models/User');
const Service = require('../models/Service');
const Order = require('../models/Order');
// const moment = require('moment')
/**
 * @desc Render Dashboard Page
 * @route /dashboard
 * @method GET 
 * @access Private ( Only Admin Can Access On This Page )
*/
module.exports.dashboard = async (req, res) => {
  try {
    // Take User Id From Cookie, and Fetch User Form DB 
    const userId = req.userId;
    const user = await User.findOne({ _id : userId });
    
    // Order Length
    const orders = await Order.find();


    return await res.status(200).render('admin/dashboard', {
      userUsername : user.username,
      userEmail : user.email,
      userBalance : user.balance,
      orderLength : orders.length
    });
  } catch (err) {
    
  };
};