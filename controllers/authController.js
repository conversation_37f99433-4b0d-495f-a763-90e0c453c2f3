// controllers/authController.js
const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * @desc Render Register Page
 * @route /register
 * @mthod GET
 * @access Public 
*/
module.exports.renderRegister = async (req, res) => {
  try {
    res.status(200).render('auth/register');
  } catch (err) {
    req.flash('error', '🔥 حصلت مشكلة غير متوقعة، حاول تاني!');
    res.status(500).redirect('/register');
  };
};
/**
 * @desc Register New User
 * @route /register
 * @mthod POST
 * @access Public 
*/
module.exports.register = async (req, res) => {
  try {
    console.log(req.body)
    // Take Data From Req
    const {
      username, email, password, confirmPassword
    } = req.body;

    // Check This User Existed Before Or No
    const exist = await User.findOne({ username, email })
    if (exist) {
      req.flash('error', '😅 الإيميل ده مستخدم قبل كده!');
      return res.redirect('/register');
    };

    // Hash The Password
    const hashed = await bcrypt.hash(password, 12);

    // Save Data In DB
    const user = await User.create({
      username, email, password: hashed,
    });

    // Generate JWT Token
    const token = jwt.sign(
      { userId : user._id }, process.env.JWT_SECRET,
      { expiresIn : process.env.JWT_TOKEN_EXPIRATION_TIME }
    );

    // Set Token In Cookie
    res.cookie('token', token, {
      httpOnly: true,
      maxAge: 7 * 24 * 60 * 60 * 1000,
      sameSite: 'Lax',
    });

    req.flash('success', '✅ تم التسجيل بنجاح! سجل دخولك دلوقتي');
    res.redirect('/login');

  } catch (err) {
    console.log(err)
    req.flash('error', '🔥 حصلت مشكلة غير متوقعة، حاول تاني!');
    res.status(500).redirect('/register'); 
  };
};


/**
 * @desc Render Login Page
 * @route /login
 * @mthod GET
 * @access Public 
*/
module.exports.renderLogin = async (req, res) => {
  try {
    res.status(200).render('auth/login');
  } catch (err) {
    req.flash('error', '🔥 حصلت مشكلة غير متوقعة، حاول تاني!');
    res.status(500).redirect('/login');
  };
};

/**
 * @desc Login Page
 * @route /login
 * @mthod POST
 * @access Public 
*/
module.exports.login = async (req, res) => {
  try{
    // Take Data From Req
    const { email, password } = req.body;

    // Check If User Exist
    const user = await User.findOne({ email }).select('+password');
    if(!user){
      req.flash('error', '😕 الحساب مش موجود')
      return res.status(404).redirect('/login')
    };

    // Check Password
    const match = await bcrypt.compare(password, user.password);
    if(!match){
      req.flash('error', '😕 الحساب مش موجود')
      return res.status(409).redirect('/login')
    };

    // Generate JWT Token
    const token = jwt.sign(
      { userId : user._id }, process.env.JWT_SECRET,
      { expiresIn : process.env.JWT_TOKEN_EXPIRATION_TIME }
    );

    // Set Token In Cookie
    res.cookie('token', token, {
      httpOnly: true,
      maxAge: 7 * 24 * 60 * 60 * 1000,
      sameSite: 'Lax',
    });
    console.log(user)
    // Redirect To Next Page
    if(user.isAdmin === true){
      req.flash('success', `✅ حمدلله علي السلامة يا ${user.username}`);
      return res.status(200).redirect('/dashboard');
    };

    req.flash('success', `✅ حمدلله علي السلامة يا ${user.username}`);
    return res.status(200).redirect('/service');

  }catch(err){
    console.log(err.message)
    req.flash('error', '🔥 حصلت مشكلة غير متوقعة، حاول تاني!');
    res.status(500).redirect('/login');
  };
};

/**
 * @desc Logout User
 * @route /logout
 * @method GET 
 * @access Private
 */
module.exports.logout = async ( req, res ) => {
  res.clearCookie('token', {
    httpOnly: true,
    sameSite: 'Lax'
  });

  // Flash Message 
  req.flash('success', '✌️ تم تسجيل الخروج بنجاح');

  // Redirect to login
  res.redirect('/login');
};