const axios = require('axios');

const User = require('../models/User');
const Order = require('../models/Order');
const Service = require('../models/Service');
const { createSmmOrder } = require('../utils/smmProvider')


module.exports.renderOrderForm = async ( req, res ) => {
  try{
    const services = await Service.find({ active : true });
      res.status(200).render('user/create-order',{
        services
      });
  }catch(err){
    console.error('❌ Error loading services:', err.message);
    res.status(500).send('حدث خطأ أثناء تحميل الخدمات');
  };
}


/**
 * @desc Order
 * @route /order
 * @method POST
 * @access Private
*/
module.exports.createOrder = async (req, res) => {
  const { serviceId, link, quantity } = req.body;
  const userId = req.userId;

  try{
    const service = await Service.findById( serviceId );
    if(!service){
      req.flash('errorMsg', 'الخدمة دي مش موجودة!');
      return res.redirect('/service');
    };

    const user = await User.findById( userId );

    if( quantity < service.minQuantity || quantity > service.maxQuantity ){
      req.flash('errorMsg', `الكمية لازم تكون بين ${service.minQuantity} و ${service.maxQuantity}`);
      return res.redirect('/service');
    };


    const totalPrice = service.pricePerUnit * quantity;

    if( user.balance < totalPrice ){
      req.flash('errorMsg', 'الرصيد غير كافي');
      return res.redirect('/service');
    };

    // طلب الخدمة من البروفايدر
    const providerResponse = await createSmmOrder({
      serviceId : service.apiServiceId,
      link,
      quantity
    });
    
    if( providerResponse.error || !providerResponse ){
      console.log('🔥 Provider Response:', providerResponse);
      req.flash('errorMsg', 'فشل في تنفيذ الطلب يرجي المحاولة مرة اخري في وقت لاحق!');
      return res.redirect('/service');
    };

    // Save Order In DB
    const newOrder = new Order({
      user : user._id,
      service : service._id,
      link,
      quantity,
      status : 'processing',
      providerOrderId : providerResponse.order,
      price : totalPrice
    });

    await newOrder.save();

    // خصم من رصيد المستخدم
    user.balance -= totalPrice,
    await user.save()
    req.flash('successMsg', 'تم إرسال الطلب بنجاح ✅ رقم الطلب');
    return res.redirect('/service');

  }catch(err){
    req.flash('errorMsg', err.message);
    res.redirect('/service');
  };
};