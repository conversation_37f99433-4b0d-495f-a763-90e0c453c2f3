// models/User.js
const mongoose = require('mongoose');
const validator = require('validator');

const userSchema = new mongoose.Schema({
  username : {
    type : String,
    required : [ true, '👤 اسم المستخدم مطلوب يا نجم!' ],
    unique : true,
    minlength : [ 3, '📏 اسم المستخدم لازم يكون 3 حروف على الأقل!' ],
    maxlength : [ 20, '🙄 كفاية كده! الاسم مينفعش يزيد عن 20 حرف!' ],
    trim : true
  },
  email : {
    type : String,
    required : [ true, '📧 الإيميل مهم يا معلم!' ],
    unique : true,
    lowercase : true,
    validate : {
      validator : validator.isEmail,
      message : '❌ الإيميل اللي كتبته شكله مش مظبوط!'
    }
  },
  password: {
    type : String,
    required : [ true, '🔒 الباسورد فين؟! متسبهوش فاضي!' ],
    minlength : [ 6, '🔑 الباسورد ضعيف أوي... خليه على الأقل 6 حروف' ],
    select : false
  },
  balance : {
    type : Number,
    default : 0,
    min : [ 0, '💸 الرصيد مينفعش يكون بالسالب يا معلم!' ]
  },

  isAdmin : {
    type : Boolean,
    default : false
  }
}, { timestamps : true });

module.exports = mongoose.model('User', userSchema);