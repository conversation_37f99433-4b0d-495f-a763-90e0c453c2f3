// models/Order.js
const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  user : {
    type : mongoose.Schema.Types.ObjectId,
    ref : 'User',
    required : [ true, '😅 المستخدم مش معروف... سجل دخول الأول!' ]
  },
  service : {
    type : mongoose.Schema.Types.ObjectId,
    ref : 'Service',
    required : [ true, '🛠️ لازم تختار خدمة الأول...' ]
  },
  link: {
    type : String,
    required : [ true, '🤔 فين اللينك يا نجم؟' ],
    validate : {
      validator : function (v) {
        return /^https?:\/\/.+$/.test(v);
      },
      message : '🔗 اللينك ده مش مظبوط... تأكد إنه بيبدأ بـ http أو https'
    }
  },
  quantity : {
    type : Number,
    required : [ true, '📦 لازم تحدد الكمية!' ],
    min : [ 1, '😬 الكمية متقلش عن 1 يا نجم!' ]
  },
  status : {
    type : String,
    enum : {
      values : [ 'pending', 'processing', 'completed', 'failed' ],
      message : '📍 الحالة اللي دخلتها مش من اللي بنستخدمها عندنا'
    },
    default : 'pending'
  },
  providerOrderId : {
    type : String,
    default : null
  },
  providerOrderId: {
    type : String,
    default : null
  },
  price: {
    type : Number,
    required : [ true, '💰 لازم تحدد السعر اللي اتحسب على المستخدم' ]
  }
}, { timestamps : true });


module.exports = mongoose.model('Order', orderSchema);