// models/Service.js
const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  name : {
    type : String,
    required : [ true, '🛠️ اسم الخدمة مطلوب يا معلم!' ],
    trim : true,
    minlength : [ 3, '🤏 اسم الخدمة قصير أوي... طوله شوية!' ],
    maxlength : [ 50, '🙄 اسم الخدمة طويل أوي... خليه مختصر ومفيد!' ]
  },
  category:  {
    type : String,
    required : [ true, '📂 لازم تحدد نوع الخدمة (متابعين، لايكات، إلخ)!' ]
  },
  pricePerUnit : {
    type : Number,
    required : [ true, '💰 سعر الوحدة مطلوب!' ],
    min : [ 0, '😬 السعر قليل أوي! مش منطقي...' ]
  },
  minQuantity : {
    type : Number,
    default : 10,
    min: [ 1, '🤏 أقل عدد لازم يكون 1 على الأقل!' ]
  },
  maxQuantity : {
    type : Number,
    default : 10000,
    max : [ 100000, '🚫 كفاية كده! الرقم كبير أوي.' ]
  },
  apiServiceId : {
    type : String,
    default : null 
  },
  active : {
    type : Boolean,
    default : true
  }
}, { timestamps : true });

module.exports = mongoose.model('Service', serviceSchema);
