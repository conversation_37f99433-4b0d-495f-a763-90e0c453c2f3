// middlewares/authMiddleware.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

module.exports.auth = async ( req, res, next ) => {
  // Get token From Cookie
  const token = req.cookies.token;
  try{
    // If No Token > Redirect
    if(!token){
      req.flash('error', '⛔ لازم تسجل دخول الأول!');
      return res.redirect('/login');
    };

    // Verify Token
    const decode = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decode.userId);
    if(!user){
      req.flash('error', '⛔ الحساب مش موجود!');
      return res.redirect('/login');
    };

    req.userId = user._id;
    next();
  }catch(err){
    console.log('Auth Error:', err.message);
    req.flash('error', '⛔ لازم تسجل دخول الأول!');
    return res.redirect('/login');
  }
};

module.exports.authorization = async ( req, res, next ) => {
  try{
    const token = req.cookies.token;
    // If No Token > Redirect
    if(!token){
      req.flash('error', '⛔ لازم تسجل دخول الأول!');
      return res.redirect('/login');
    };

    // Verify Token
    const decode = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decode.userId);
    if(!user){
      req.flash('error', '⛔ الحساب مش موجود!');
      return res.redirect('/login');
    };
    
    if(user.isAdmin === false){
      req.flash('error', 'ممنوع الوصول للصفحة دي!');
      return res.redirect('/login');
    }else if( user.isAdmin === true ){
      req.userId = user._id;
      next();
    };
  }catch(err){
    console.log('Auth Error:', err);
    req.flash('error', '⛔ لازم تسجل دخول الأول!');
    return res.redirect('/login');
  };
};